
'use server';
/**
 * @fileOverview Removes the background from an image using Genkit and Gemini.
 * 
 * - removeBackgroundFromImage - A function that takes an image data URI and returns a new data URI with a transparent background.
 * - RemoveBackgroundInput - The input type for the function.
 * - RemoveBackgroundOutput - The return type for the function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { Tool } from 'genkit/ai';

const RemoveBackgroundInputSchema = z.object({
  photoDataUri: z
    .string()
    .describe(
      "A photo of a person or object, as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."
    ),
});
export type RemoveBackgroundInput = z.infer<typeof RemoveBackgroundInputSchema>;

const RemoveBackgroundOutputSchema = z.object({
  processedImageUri: z.string().describe('The processed image with a transparent background as a data URI.'),
});
export type RemoveBackgroundOutput = z.infer<typeof RemoveBackgroundOutputSchema>;

// This function will be called from the client-side component.
export async function removeBackgroundFromImage(input: RemoveBackgroundInput): Promise<RemoveBackgroundOutput> {
  return removeBackgroundFlow(input);
}

const removeBackgroundFlow = ai.defineFlow(
  {
    name: 'removeBackgroundFlow',
    inputSchema: RemoveBackgroundInputSchema,
    outputSchema: RemoveBackgroundOutputSchema,
  },
  async (input) => {
    try {
      const { media } = await ai.generate({
        model: googleAI.model('gemini-2.0-flash-preview-image-generation'),
        prompt: [
          { media: { url: input.photoDataUri } },
          { text: "Remove the background from this image. The output should be a PNG with a transparent background, keeping the original subject's dimensions and quality." },
        ],
        config: {
          responseModalities: ['IMAGE'],
        },
      });

      if (!media?.url) {
        throw new Error("Image generation failed to return an image.");
      }

      return {
        processedImageUri: media.url,
      };

    } catch (error) {
      console.error('Error in removeBackgroundFlow:', error);
      throw new Error('An unexpected error occurred while processing the image with Genkit.');
    }
  }
);

'use client';

import { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { getToolRatingStats, type ToolRatingStats } from '@/lib/ratings';

interface ToolRatingDisplayProps {
  toolSlug: string;
  showCount?: boolean;
  size?: 'sm' | 'md';
}

export default function ToolRatingDisplay({ 
  toolSlug, 
  showCount = true, 
  size = 'sm' 
}: ToolRatingDisplayProps) {
  const [stats, setStats] = useState<ToolRatingStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      try {
        const data = await getToolRatingStats(toolSlug);
        setStats(data);
      } catch (error) {
        console.error('Error loading rating stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [toolSlug]);

  const renderStars = (rating: number) => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4'
    };

    return (
      <div className="flex items-center space-x-1 space-x-reverse">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= Math.round(rating)
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2 space-x-reverse animate-pulse">
        <div className="flex space-x-1 space-x-reverse">
          {[1, 2, 3, 4, 5].map((star) => (
            <div key={star} className={`${size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'} bg-gray-200 rounded`}></div>
          ))}
        </div>
        {showCount && <div className="h-4 w-8 bg-gray-200 rounded"></div>}
      </div>
    );
  }

  if (!stats || stats.total_ratings === 0) {
    return (
      <div className="flex items-center space-x-2 space-x-reverse text-gray-400">
        {renderStars(0)}
        {showCount && (
          <span className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-gray-500`}>
            لا توجد تقييمات
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 space-x-reverse">
      {renderStars(stats.average_rating)}
      {showCount && (
        <span className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-gray-600`}>
          {stats.average_rating.toFixed(1)} ({stats.total_ratings})
        </span>
      )}
    </div>
  );
}

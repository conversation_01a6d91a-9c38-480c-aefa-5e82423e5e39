
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Copy, FileText, RefreshCw, FileDown, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { saveAs } from 'file-saver';
import { generateDocx } from '@/lib/actions/docx';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';


const FormSchema = z.object({
  employeeName: z.string().min(1, 'الرجاء إدخال اسمك الكامل.'),
  employeeGender: z.enum(['male', 'female'], { required_error: 'الرجاء تحديد جنس الموظف.' }),
  jobTitle: z.string().min(1, 'الرجاء إدخال منصبك الحالي.'),
  managerName: z.string().min(1, 'الرجاء إدخال اسم المدير المباشر.'),
  managerGender: z.enum(['male', 'female'], { required_error: 'الرجاء تحديد جنس المدير.' }),
  companyName: z.string().min(1, 'الرجاء إدخال اسم الشركة.'),
  lastDay: z.string().min(1, 'الرجاء إدخال تاريخ آخر يوم عمل.'),
  template: z.enum(['appreciation', 'family_reasons', 'formal', 'detailed_formal']).default('appreciation'),
  dateType: z.enum(['gregorian', 'hijri', 'none']).default('gregorian'),
});

type FormValues = z.infer<typeof FormSchema>;

const getDateString = (dateType: 'gregorian' | 'hijri' | 'none', mounted: boolean = true) => {
    if (!mounted) return '';

    const today = new Date();
    try {
        switch (dateType) {
            case 'gregorian':
                 return new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                }).format(today);
            case 'hijri':
                return new Intl.DateTimeFormat('ar-SA-u-ca-islamic-nu-latn', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                }).format(today);
            case 'none':
                return '../../....';
            default:
                return '';
        }
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateType === 'none' ? '../../....' : today.toLocaleDateString();
    }
};

const templates: { [key: string]: { label: string, generator: (data: FormValues) => string } } = {
  appreciation: {
    label: "خطاب استقالة محترم مع الشكر",
    generator: (data) => {
      const managerTitle = data.managerGender === 'male' ? 'السيد' : 'السيدة';
      const managerAdjective = data.managerGender === 'male' ? 'المحترم' : 'المحترمة';
      return `
  ${managerTitle} ${data.managerName} ${managerAdjective}،
  
  تحية طيبة وبعد،
  
  أود أن أبلغكم برغبتي في تقديم استقالتي من منصبي في ${data.companyName}، وذلك اعتبارًا من تاريخ ${data.lastDay}.
  
  لقد كانت تجربتي في العمل معكم وفريق العمل فرصة قيّمة تعلمت من خلالها الكثير، وأشعر بالامتنان للفرص التي أتيحت لي وللثقة التي وضعت فيّ. ومع ذلك، وبعد تفكير عميق، أعتقد أن الوقت قد حان لأن أتخذ مسارًا جديدًا في مسيرتي المهنية.
  
  أتقدم لكم بخالص الشكر والتقدير على الدعم والتعاون الذي لاقيته خلال فترة عملي، وأتمنى لكم دوام النجاح والتوفيق. سأبذل قصارى جهدي لضمان انتقال سلس وتسليم جميع المهام والمسؤوليات بشكل يضمن استمرارية العمل.
  
  مع جزيل الشكر والاحترام،
  
  الاسم: ${data.employeeName}
  التوقيع: ...........................
  تاريخ اليوم: ${getDateString(data.dateType)}
      `.trim();
    }
  },
  family_reasons: {
    label: "استقالة لظروف عائلية",
    generator: (data) => {
      const managerTitle = data.managerGender === 'male' ? 'السيد' : 'السيدة';
      const managerAdjective = data.managerGender === 'male' ? 'المحترم' : 'المحترمة';
      return `
  ${managerTitle} ${data.managerName} ${managerAdjective}،
  
  تحية طيبة وبعد،
  
  أود أن أبلغكم برغبتي في تقديم استقالتي من وظيفتي كـ${data.jobTitle} اعتبارًا من ${data.lastDay}.
  
  لقد قررت ترك الوظيفة لأسباب شخصية تتعلق بظروف عائلية.
  
  أود أن أشكركم جزيل الشكر على الفرصة التي أتيحت لي للعمل معكم، وللتجارب الرائعة التي مررت بها في شركتكم.
  
  أتمنى للشركة دوام النجاح والتوفيق، وأؤكد استعدادي لتقديم أي مساعدة خلال فترة الانتقال.
  
  مع خالص الشكر والتقدير،
  
  الاسم: ${data.employeeName}
  التوقيع: ...........................
  تاريخ اليوم: ${getDateString(data.dateType)}
      `.trim();
    }
  },
  formal: {
    label: "صيغة رسمية",
    generator: (data) => {
      const managerTitle = data.managerGender === 'male' ? 'السيد' : 'السيدة';
      const managerAdjective = data.managerGender === 'male' ? 'المحترم' : 'المحترمة';
      return `
  ${managerTitle} ${data.managerName} ${managerAdjective}،
  
  تحية طيبة وبعد،
  
  يرجى قبول هذا الخطاب كإشعار رسمي بتقديم استقالتي من منصبي كـ${data.jobTitle} في شركة ${data.companyName}. سيكون آخر يوم عمل لي هو ${data.lastDay}.
  
  أنا ممتن للفرصة التي أتيحت لي خلال فترة عملي هنا، وأقدر الخبرة التي اكتسبتها. وأؤكد لكم التزامي الكامل بضمان انتقال سلس للمسؤوليات قبل مغادرتي.
  
  أتمنى لكم وللشركة كل التوفيق والنجاح في المستقبل.
  
  مع خالص التقدير،
  
  الاسم: ${data.employeeName}
  التوقيع: ...........................
  تاريخ اليوم: ${getDateString(data.dateType)}
      `.trim();
    }
  },
  detailed_formal: {
    label: "نموذج استقالة بالعربي (مفصل)",
    generator: (data) => {
      const managerTitle = data.managerGender === 'male' ? 'السيد' : 'السيدة';
      return `
الاسم: ${data.employeeName}
المنصب: ${data.jobTitle}
تاريخ الاستقالة: ${data.lastDay}

${managerTitle} ${data.managerName},
أتقدم بطلب الاستقالة من منصبي الحالي في ${data.companyName}، وذلك اعتبارًا من تاريخ ${data.lastDay}.

أود أن أعرب عن تقديري العميق للفرصة التي منحتموني إياها للعمل في هذه المؤسسة المحترمة والتعاون مع فريق العمل المميز.

بعد النظر والتفكير العميق، اتخذت قرارًا صعبًا للابتعاد عن هذا المنصب والسعي إلى تحقيق طموحاتي الشخصية والمهنية في مجال آخر.

أرغب في أن يكون هذا الانتهاء عمليًا وسلسًا، ولذلك أنا مستعد للمساعدة في تسهيل عملية الانتقال وتدريب الشخص الذي سيتولى مسؤولياتي بعد رحيلي. كما أنني مستعد لاستكمال أي مهام ملحة أو تسليم العمل العالق قبل آخر يوم عمل لي معكم المتفق عليه.

أود أن أشكركم مرة أخرى على جميع الفرص والدعم التي قدمتموها لي خلال فترة عملي هنا. قد استفدت كثيرًا من تجربتي وتعاوني مع الفريق، وأتطلع إلى الاحتفاظ بالعلاقات المهنية المبنية خلال فترة وجودي هنا.

أعتذر عن أي إزعاج قد يسببه هذا القرار، وأتمنى لكم وللشركة المزيد من التقدم والنجاح في المستقبل.

مع خالص التحية،
${data.employeeName}
${data.jobTitle}
${getDateString(data.dateType)}
      `.trim();
    }
  },
};

export function ResignationLetterGeneratorTool() {
  const [result, setResult] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      template: 'appreciation',
      employeeGender: 'male',
      managerGender: 'male',
      companyName: '',
      employeeName: '',
      jobTitle: '',
      lastDay: '',
      dateType: 'gregorian',
    },
  });

  const onSubmit = (data: FormValues) => {
    const generator = templates[data.template].generator;
    const letter = generator(data);
    setResult(letter);
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result).then(() => {
        toast({
          title: 'تم النسخ بنجاح!',
          description: 'تم نسخ خطاب الاستقالة إلى الحافظة.',
        });
      });
    }
  };

  const handleDownload = async () => {
    if (!result) return;
    setIsDownloading(true);
    try {
        const fileContentBase64 = await generateDocx(result);
        if (fileContentBase64) {
          const blob = new Blob([Buffer.from(fileContentBase64, 'base64')], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
          saveAs(blob, 'خطاب_استقالة.docx');
          toast({
            title: 'تم التحميل بنجاح!',
            description: 'تم تحميل الخطاب كملف Word.',
          });
        } else {
          throw new Error('فشل إنشاء الملف.');
        }
    } catch (error) {
        console.error('Error generating DOCX:', error);
        toast({
          variant: 'destructive',
          title: 'فشل التحميل',
          description: error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء ملف Word.',
        });
    } finally {
        setIsDownloading(false);
    }
  };


  const resetForm = () => {
    form.reset({
      employeeName: '',
      jobTitle: '',
      managerName: '',
      companyName: '',
      lastDay: '',
      template: 'appreciation',
      employeeGender: 'male',
      managerGender: 'male',
      dateType: 'gregorian',
    });
    setResult('');
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>بيانات الخطاب</span>
            <Button variant="ghost" size="sm" onClick={resetForm}>
              <RefreshCw className="ml-2 h-4 w-4" />
              مسح الكل
            </Button>
          </CardTitle>
          <CardDescription>
            املأ الحقول أدناه لإنشاء خطاب استقالة احترافي.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="employeeName" render={({ field }) => (<FormItem><FormLabel>اسمك الكامل</FormLabel><FormControl><Input placeholder="مثال: محمد الأحمد" {...field} /></FormControl><FormMessage /></FormItem>)} />
                <FormField control={form.control} name="employeeGender" render={({ field }) => (
                  <FormItem><FormLabel>جنسك</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex gap-4 pt-2 " dir="rtl">
                          <div className="flex items-center gap-2 flex-row-reverse"><FormLabel className="font-normal">ذكر</FormLabel><RadioGroupItem value="male" /></div>
                          <div className="flex items-center gap-2 flex-row-reverse"><FormLabel className="font-normal">أنثى</FormLabel><RadioGroupItem value="female" /></div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              
              <FormField control={form.control} name="jobTitle" render={({ field }) => (<FormItem><FormLabel>منصبك الحالي</FormLabel><FormControl><Input placeholder="مثال: مهندس برمجيات" {...field} /></FormControl><FormMessage /></FormItem>)} />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="managerName" render={({ field }) => (<FormItem><FormLabel>اسم المدير المباشر</FormLabel><FormControl><Input placeholder="مثال: سارة العبدالله" {...field} /></FormControl><FormMessage /></FormItem>)} />
                <FormField control={form.control} name="managerGender" render={({ field }) => (
                  <FormItem><FormLabel>جنس المدير</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex gap-4 pt-2 " dir="rtl">
                          <div className="flex items-center gap-2 flex-row-reverse"><FormLabel className="font-normal">ذكر</FormLabel><RadioGroupItem value="male" /></div>
                          <div className="flex items-center gap-2 flex-row-reverse"><FormLabel className="font-normal">أنثى</FormLabel><RadioGroupItem value="female" /></div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              
              <FormField control={form.control} name="companyName" render={({ field }) => (<FormItem><FormLabel>اسم الشركة</FormLabel><FormControl><Input placeholder="مثال: شركة التقنية المتقدمة" {...field} /></FormControl><FormMessage /></FormItem>)} />
              
              <FormField control={form.control} name="lastDay" render={({ field }) => (<FormItem><FormLabel>تاريخ آخر يوم عمل</FormLabel><FormControl><Input placeholder="مثال: 31 ديسمبر 2024" {...field} /></FormControl><FormMessage /></FormItem>)} />
              
              <FormField
                  control={form.control}
                  name="template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>اختر قالب الخطاب</FormLabel>
                       <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl className='flex-row-reverse'>
                            <SelectTrigger>
                                <SelectValue  placeholder="اختر نوع القالب" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent >
                              {Object.entries(templates).map(([key, value]) => (
                                  <SelectItem key={key} value={key}>{value.label}</SelectItem>
                              ))}
                          </SelectContent>
                       </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              <FormField
                control={form.control}
                name="dateType"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>تنسيق تاريخ اليوم</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={String(field.value)}
                        className="grid grid-cols-1 md:grid-cols-3 gap-2"
                        
                      >
                        <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse ">
                          
                          <RadioGroupItem value="gregorian" />
                          <span>تاريخ ميلادي</span>
                        </Label>
                        <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse">
                          
                          <RadioGroupItem value="hijri" />
                          <span>تاريخ هجري</span>
                        </Label>
                         <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse">
                          
                          <RadioGroupItem value="none" />
                          <span>بدون تاريخ</span>
                        </Label>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full">
                <FileText className="ml-2 h-4 w-4" />
                إنشاء الخطاب
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      
      <Card className="bg-muted/30 flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>الخطاب النهائي</span>
          </CardTitle>
          <CardDescription>هذا هو خطاب الاستقالة الجاهز للنسخ أو التحميل.</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col flex-1">
          <Textarea
            readOnly
            value={result || 'سيظهر الخطاب النهائي هنا بعد ملء البيانات...'}
            className="min-h-[400px] whitespace-pre-wrap leading-loose bg-background flex-1"
          />
           {result && 
              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                 <Button variant="outline" className="flex-1" onClick={handleDownload} disabled={isDownloading}>
                    {isDownloading ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <FileDown className="ml-2 h-4 w-4" />}
                     {isDownloading ? 'جاري التحميل...' : 'تحميل Word'}
                 </Button>
                 <Button variant="outline" className="flex-1" onClick={copyToClipboard}><Copy className="ml-2 h-4 w-4" /> نسخ النص</Button>
              </div>
            }
        </CardContent>
      </Card>
    </div>
  );
}


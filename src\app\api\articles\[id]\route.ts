import { NextRequest, NextResponse } from 'next/server';
import {
  getArticleById,
  updateArticle,
  deleteArticle,
  incrementArticleViews,
  incrementArticleLikes
} from '@/lib/articles-supabase';
import { UpdateArticleData } from '@/types/article';

interface RouteParams {
  params: { id: string };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const article = await getArticleById(id);
    
    if (!article) {
      return NextResponse.json(
        { error: 'المقال غير موجود' },
        { status: 404 }
      );
    }

    // Check if we should increment views
    const { searchParams } = new URL(request.url);
    if (searchParams.get('incrementViews') === 'true') {
      await incrementArticleViews(id);
    }

    return NextResponse.json({ article });
  } catch (error) {
    console.error('Error in GET /api/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المقال' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    // Validate that article exists
    const existingArticle = await getArticleById(id);
    if (!existingArticle) {
      return NextResponse.json(
        { error: 'المقال غير موجود' },
        { status: 404 }
      );
    }

    const updateData: UpdateArticleData = {
      id,
      ...body,
    };

    await updateArticle(updateData);
    
    return NextResponse.json({ message: 'تم تحديث المقال بنجاح' });
  } catch (error) {
    console.error('Error in PUT /api/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المقال' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    
    // Validate that article exists
    const existingArticle = await getArticleById(id);
    if (!existingArticle) {
      return NextResponse.json(
        { error: 'المقال غير موجود' },
        { status: 404 }
      );
    }

    await deleteArticle(id);
    
    return NextResponse.json({ message: 'تم حذف المقال بنجاح' });
  } catch (error) {
    console.error('Error in DELETE /api/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المقال' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'like') {
      await incrementArticleLikes(id);
      return NextResponse.json({ message: 'تم إضافة الإعجاب' });
    }

    if (action === 'view') {
      await incrementArticleViews(id);
      return NextResponse.json({ message: 'تم تسجيل المشاهدة' });
    }

    return NextResponse.json(
      { error: 'إجراء غير صحيح' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error in PATCH /api/articles/[id]:', error);
    return NextResponse.json(
      { error: 'فشل في تنفيذ الإجراء' },
      { status: 500 }
    );
  }
}

'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from '@/components/PageHeader';
import { 
  Shield, 
  User, 
  Key,
  Eye,
  UserPlus,
  Trash2,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useAuth } from '@/components/AuthProvider';
import { isAdmin } from '@/lib/auth-supabase';

export default function PermissionsTestPage() {
  const { user } = useAuth();
  const userIsAdmin = isAdmin(user);

  const permissions = [
    {
      name: 'رؤية جميع كلمات المرور',
      icon: Eye,
      adminOnly: true,
      description: 'إمكانية رؤية كلمات مرور جميع المستخدمين في الجداول'
    },
    {
      name: 'تغيير كلمة مرور أي مستخدم',
      icon: Key,
      adminOnly: true,
      description: 'تغيير كلمة مرور أي مستخدم بدون الحاجة لكلمة المرور القديمة'
    },
    {
      name: 'إضافة مستخدمين جدد',
      icon: UserPlus,
      adminOnly: true,
      description: 'إنشاء حسابات مستخدمين جديدة مع تحديد الأدوار'
    },
    {
      name: 'حذف المستخدمين',
      icon: Trash2,
      adminOnly: true,
      description: 'حذف حسابات المستخدمين (عدا الحساب الشخصي)'
    },
    {
      name: 'تغيير كلمة المرور الشخصية',
      icon: Key,
      adminOnly: false,
      description: 'تغيير كلمة مرور الحساب الشخصي مع التحقق من كلمة المرور القديمة'
    },
    {
      name: 'رؤية المعلومات الشخصية',
      icon: User,
      adminOnly: false,
      description: 'عرض وإدارة معلومات الحساب الشخصي'
    }
  ];

  if (!user) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="اختبار الصلاحيات" 
          description="عرض الصلاحيات المتاحة للمستخدم الحالي"
        />
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-gray-500">يرجى تسجيل الدخول أولاً</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="اختبار الصلاحيات" 
        description="عرض الصلاحيات المتاحة للمستخدم الحالي"
      />

      {/* معلومات المستخدم الحالي */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <User className="h-5 w-5" />
            <span>المستخدم الحالي</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600">اسم المستخدم</p>
              <p className="font-medium">{user.username}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">الاسم الكامل</p>
              <p className="font-medium">{user.full_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">الدور</p>
              <Badge variant={userIsAdmin ? "default" : "secondary"}>
                {user.role}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-600">نوع الصلاحيات</p>
              <Badge variant={userIsAdmin ? "default" : "outline"}>
                {userIsAdmin ? 'إدارية' : 'محدودة'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الصلاحيات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Shield className="h-5 w-5" />
            <span>جدول الصلاحيات</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الصلاحية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {permissions.map((permission, index) => {
                  const hasPermission = permission.adminOnly ? userIsAdmin : true;
                  const IconComponent = permission.icon;
                  
                  return (
                    <tr key={index} className={`hover:bg-gray-50 ${hasPermission ? '' : 'opacity-60'}`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <IconComponent className={`h-4 w-4 ${hasPermission ? 'text-blue-600' : 'text-gray-400'}`} />
                          <span className="text-sm font-medium text-gray-900">
                            {permission.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <p className="text-sm text-gray-600">
                          {permission.description}
                        </p>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge variant={permission.adminOnly ? "destructive" : "secondary"}>
                          {permission.adminOnly ? 'إدارية فقط' : 'عامة'}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {hasPermission ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-sm text-green-600 font-medium">متاحة</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 text-red-600" />
                              <span className="text-sm text-red-600 font-medium">غير متاحة</span>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* ملاحظات الاختبار */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-900">ملاحظات الاختبار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-blue-800">
            <div className="flex items-start space-x-2 space-x-reverse">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p>
                <strong>للاختبار كإداري:</strong> سجل الدخول بـ admin/admin123 أو manager/manager123
              </p>
            </div>
            <div className="flex items-start space-x-2 space-x-reverse">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p>
                <strong>للاختبار كمستخدم عادي:</strong> سجل الدخول بـ editor/editor123
              </p>
            </div>
            <div className="flex items-start space-x-2 space-x-reverse">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <p>
                <strong>الصفحات للاختبار:</strong> /admin/credentials (إدارة المستخدمين) و /admin/profile (الملف الشخصي)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

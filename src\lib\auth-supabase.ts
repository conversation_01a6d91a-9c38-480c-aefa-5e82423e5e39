import { supabase } from './supabase';
import bcrypt from 'bcryptjs';

export interface AdminUser {
  id: string;
  username: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  last_login: string | null;
  created_at: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface CreateUserData {
  username: string;
  email?: string;
  password: string;
  fullName: string;
  role?: string;
}

// تسجيل الدخول
export const loginUser = async (credentials: LoginCredentials): Promise<AdminUser | null> => {
  try {
    const { username, password } = credentials;

    // البحث عن المستخدم
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      console.error('User not found:', error);
      return null;
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      console.error('Invalid password');
      return null;
    }

    // تحديث آخر تسجيل دخول
    await supabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id);

    // إرجاع بيانات المستخدم بدون كلمة المرور
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword as AdminUser;

  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};

// إنشاء مستخدم جديد
export const createUser = async (userData: CreateUserData): Promise<AdminUser | null> => {
  try {
    const { username, email, password, fullName, role = 'editor' } = userData;

    // تشفير كلمة المرور
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);

    // إنشاء المستخدم
    const { data: user, error } = await supabase
      .from('admin_users')
      .insert({
        username,
        email,
        password_hash,
        full_name: fullName,
        role,
        is_active: true
      })
      .select('id, username, email, full_name, role, is_active, last_login, created_at')
      .single();

    if (error) {
      console.error('Error creating user:', error);
      // إذا كان الخطأ متعلق بالتكرار، نرمي رسالة خاصة
      if (error.code === '23505') {
        throw new Error('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل');
      }
      throw new Error(`خطأ في إنشاء المستخدم: ${error.message}`);
    }

    return user as AdminUser;

  } catch (error) {
    console.error('Create user error:', error);
    // إذا كان الخطأ من نوع Error، نرميه مرة أخرى ليتم التعامل معه في المكون
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('حدث خطأ غير متوقع أثناء إنشاء المستخدم');
  }
};

// تغيير كلمة المرور
export const changePassword = async (userId: string, oldPassword: string, newPassword: string): Promise<boolean> => {
  try {
    // الحصول على كلمة المرور الحالية
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('password_hash')
      .eq('id', userId)
      .single();

    if (error || !user) {
      console.error('User not found:', error);
      return false;
    }

    // التحقق من كلمة المرور القديمة
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password_hash);
    
    if (!isOldPasswordValid) {
      console.error('Invalid old password');
      return false;
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 10;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // تحديث كلمة المرور
    const { error: updateError } = await supabase
      .from('admin_users')
      .update({ password_hash: newPasswordHash })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating password:', updateError);
      return false;
    }

    return true;

  } catch (error) {
    console.error('Change password error:', error);
    return false;
  }
};

// الحصول على جميع المستخدمين
export const getAllUsers = async (): Promise<AdminUser[]> => {
  try {
    const { data: users, error } = await supabase
      .from('admin_users')
      .select('id, username, email, full_name, role, is_active, last_login, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      return [];
    }

    return users as AdminUser[];

  } catch (error) {
    console.error('Get all users error:', error);
    return [];
  }
};

// تحديث حالة المستخدم (تفعيل/إلغاء تفعيل)
export const updateUserStatus = async (userId: string, isActive: boolean): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('admin_users')
      .update({ is_active: isActive })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user status:', error);
      return false;
    }

    return true;

  } catch (error) {
    console.error('Update user status error:', error);
    return false;
  }
};

// تغيير كلمة مرور أي مستخدم (للإداريين فقط)
export const changeUserPassword = async (userId: string, newPassword: string): Promise<boolean> => {
  try {
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    const { error } = await supabase
      .from('admin_users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error changing user password:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error changing user password:', error);
    return false;
  }
};

// حذف مستخدم (للإداريين فقط)
export const deleteUser = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('admin_users')
      .delete()
      .eq('id', userId);

    if (error) {
      console.error('Error deleting user:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    return false;
  }
};

// التحقق من صلاحيات الإدارة
export const isAdmin = (user: AdminUser | null): boolean => {
  return user?.role === 'admin';
};

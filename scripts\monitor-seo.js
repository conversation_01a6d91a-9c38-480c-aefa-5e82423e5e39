#!/usr/bin/env node

/**
 * أداة مراقبة SEO تلقائية لموقع أدوات بالعربي
 * تراقب الموقع وترسل تقارير دورية
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

class SEOMonitor {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.logFile = path.join(__dirname, '..', 'seo-monitoring.log');
    this.reportFile = path.join(__dirname, '..', 'seo-report.json');
  }

  async checkSiteStatus() {
    const timestamp = new Date().toISOString();
    console.log(`🔍 بدء مراقبة SEO - ${timestamp}`);
    
    const results = {
      timestamp,
      status: {},
      performance: {},
      issues: [],
      recommendations: []
    };

    try {
      // فحص الصفحة الرئيسية
      const homePageResult = await this.checkPage(this.baseUrl);
      results.status.homePage = homePageResult;

      // فحص sitemap
      const sitemapResult = await this.checkSitemap();
      results.status.sitemap = sitemapResult;

      // فحص robots
      const robotsResult = await this.checkRobots();
      results.status.robots = robotsResult;

      // فحص صفحات مهمة
      const importantPages = [
        '/tools/zakat-calculator',
        '/tools/date-converter',
        '/tools/age-calculator',
        '/tools/currency-converter'
      ];

      results.status.importantPages = {};
      for (const page of importantPages) {
        const pageResult = await this.checkPage(`${this.baseUrl}${page}`);
        results.status.importantPages[page] = pageResult;
      }

      // تحليل النتائج
      this.analyzeResults(results);

      // حفظ التقرير
      await this.saveReport(results);

      // طباعة التقرير
      this.printReport(results);

      return results;

    } catch (error) {
      console.error('❌ خطأ في مراقبة SEO:', error.message);
      results.issues.push(`خطأ عام: ${error.message}`);
      return results;
    }
  }

  async checkPage(url) {
    try {
      const startTime = Date.now();
      const response = await this.fetchUrl(url);
      const loadTime = Date.now() - startTime;

      const result = {
        url,
        statusCode: response.statusCode,
        loadTime,
        accessible: response.statusCode === 200,
        size: response.body.length,
        hasTitle: response.body.includes('<title>'),
        hasDescription: response.body.includes('name="description"'),
        hasH1: response.body.includes('<h1'),
        hasStructuredData: response.body.includes('application/ld+json'),
        timestamp: new Date().toISOString()
      };

      if (response.statusCode !== 200) {
        result.error = `HTTP ${response.statusCode}`;
      }

      if (loadTime > 3000) {
        result.warning = 'بطء في التحميل (أكثر من 3 ثواني)';
      }

      return result;

    } catch (error) {
      return {
        url,
        accessible: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  async checkSitemap() {
    try {
      const sitemapUrl = `${this.baseUrl}/sitemap.xml`;
      const response = await this.fetchUrl(sitemapUrl);

      if (response.statusCode === 200) {
        const urlCount = (response.body.match(/<url>/g) || []).length;
        return {
          accessible: true,
          urlCount,
          size: response.body.length,
          lastModified: response.headers['last-modified'] || 'غير محدد'
        };
      } else {
        return {
          accessible: false,
          error: `HTTP ${response.statusCode}`
        };
      }
    } catch (error) {
      return {
        accessible: false,
        error: error.message
      };
    }
  }

  async checkRobots() {
    try {
      const robotsUrl = `${this.baseUrl}/robots.txt`;
      const response = await this.fetchUrl(robotsUrl);

      if (response.statusCode === 200) {
        const body = response.body.toLowerCase();
        return {
          accessible: true,
          hasSitemap: body.includes('sitemap:'),
          allowsAll: body.includes('allow: /'),
          size: response.body.length
        };
      } else {
        return {
          accessible: false,
          error: `HTTP ${response.statusCode}`
        };
      }
    } catch (error) {
      return {
        accessible: false,
        error: error.message
      };
    }
  }

  analyzeResults(results) {
    // تحليل الصفحة الرئيسية
    const homePage = results.status.homePage;
    if (!homePage.accessible) {
      results.issues.push('الصفحة الرئيسية غير متاحة');
    } else {
      if (homePage.loadTime > 3000) {
        results.issues.push(`الصفحة الرئيسية بطيئة (${homePage.loadTime}ms)`);
      }
      if (!homePage.hasTitle) {
        results.issues.push('الصفحة الرئيسية لا تحتوي على title');
      }
      if (!homePage.hasDescription) {
        results.issues.push('الصفحة الرئيسية لا تحتوي على meta description');
      }
    }

    // تحليل sitemap
    if (!results.status.sitemap.accessible) {
      results.issues.push('sitemap.xml غير متاح');
    } else if (results.status.sitemap.urlCount < 100) {
      results.recommendations.push('أضف المزيد من الصفحات إلى sitemap');
    }

    // تحليل robots
    if (!results.status.robots.accessible) {
      results.issues.push('robots.txt غير متاح');
    } else if (!results.status.robots.hasSitemap) {
      results.issues.push('robots.txt لا يحتوي على رابط sitemap');
    }

    // تحليل الصفحات المهمة
    const importantPages = results.status.importantPages;
    let inaccessiblePages = 0;
    for (const [page, status] of Object.entries(importantPages)) {
      if (!status.accessible) {
        inaccessiblePages++;
        results.issues.push(`صفحة مهمة غير متاحة: ${page}`);
      }
    }

    if (inaccessiblePages === 0) {
      results.recommendations.push('جميع الصفحات المهمة متاحة ✅');
    }

    // توصيات عامة
    if (results.issues.length === 0) {
      results.recommendations.push('الموقع يعمل بشكل جيد تقنياً');
    } else {
      results.recommendations.push('ركز على حل المشاكل التقنية أولاً');
    }
  }

  async saveReport(results) {
    try {
      // حفظ التقرير الكامل
      fs.writeFileSync(this.reportFile, JSON.stringify(results, null, 2));

      // إضافة سجل مختصر
      const logEntry = `${results.timestamp} - Issues: ${results.issues.length}, HomePage: ${results.status.homePage.accessible ? 'OK' : 'FAIL'}, LoadTime: ${results.status.homePage.loadTime || 'N/A'}ms\n`;
      fs.appendFileSync(this.logFile, logEntry);

    } catch (error) {
      console.error('خطأ في حفظ التقرير:', error.message);
    }
  }

  printReport(results) {
    console.log('\n📊 تقرير مراقبة SEO');
    console.log('='.repeat(50));

    // حالة الموقع العامة
    const homePage = results.status.homePage;
    console.log(`\n🏠 الصفحة الرئيسية: ${homePage.accessible ? '✅ متاحة' : '❌ غير متاحة'}`);
    if (homePage.accessible) {
      console.log(`   ⏱️ وقت التحميل: ${homePage.loadTime}ms`);
      console.log(`   📄 الحجم: ${(homePage.size / 1024).toFixed(1)} KB`);
      console.log(`   🏷️ Title: ${homePage.hasTitle ? '✅' : '❌'}`);
      console.log(`   📝 Description: ${homePage.hasDescription ? '✅' : '❌'}`);
      console.log(`   📊 Structured Data: ${homePage.hasStructuredData ? '✅' : '❌'}`);
    }

    // حالة sitemap
    const sitemap = results.status.sitemap;
    console.log(`\n🗺️ Sitemap: ${sitemap.accessible ? '✅ متاح' : '❌ غير متاح'}`);
    if (sitemap.accessible) {
      console.log(`   📊 عدد الصفحات: ${sitemap.urlCount}`);
    }

    // حالة robots
    const robots = results.status.robots;
    console.log(`\n🤖 Robots.txt: ${robots.accessible ? '✅ متاح' : '❌ غير متاح'}`);
    if (robots.accessible) {
      console.log(`   🗺️ يحتوي على sitemap: ${robots.hasSitemap ? '✅' : '❌'}`);
    }

    // الصفحات المهمة
    console.log('\n📄 الصفحات المهمة:');
    for (const [page, status] of Object.entries(results.status.importantPages)) {
      console.log(`   ${status.accessible ? '✅' : '❌'} ${page} (${status.loadTime || 'N/A'}ms)`);
    }

    // المشاكل
    if (results.issues.length > 0) {
      console.log('\n⚠️ المشاكل المكتشفة:');
      results.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

    // التوصيات
    if (results.recommendations.length > 0) {
      console.log('\n💡 التوصيات:');
      results.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    console.log('\n' + '='.repeat(50));
    console.log(`✅ انتهت المراقبة - ${new Date().toLocaleString('ar-SA')}`);
  }

  async fetchUrl(url) {
    return new Promise((resolve, reject) => {
      const req = https.get(url, {
        headers: {
          'User-Agent': 'SEO-Monitor/1.0'
        }
      }, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        });
      });

      req.on('error', reject);
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('انتهت مهلة الاتصال'));
      });
    });
  }

  // تشغيل مراقبة دورية
  startPeriodicMonitoring(intervalHours = 6) {
    console.log(`🔄 بدء المراقبة الدورية كل ${intervalHours} ساعات`);
    
    // تشغيل فوري
    this.checkSiteStatus();
    
    // تشغيل دوري
    setInterval(() => {
      this.checkSiteStatus();
    }, intervalHours * 60 * 60 * 1000);
  }
}

// تشغيل المراقب
if (require.main === module) {
  const baseUrl = process.argv[2] || 'https://adawat.org';
  const monitor = new SEOMonitor(baseUrl);
  
  if (process.argv.includes('--continuous')) {
    monitor.startPeriodicMonitoring(6); // كل 6 ساعات
  } else {
    monitor.checkSiteStatus().catch(console.error);
  }
}

module.exports = SEOMonitor;

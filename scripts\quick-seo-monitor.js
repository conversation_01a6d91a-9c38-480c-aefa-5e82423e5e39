#!/usr/bin/env node

/**
 * أداة مراقبة سريعة لحالة SEO للموقع
 * تفحص الجوانب الأساسية وتنبه لأي مشاكل
 */

const https = require('https');
const fs = require('fs');

const SITE_URL = 'https://adawat.org';
const CHECK_INTERVAL = 30 * 60 * 1000; // 30 دقيقة

class QuickSEOMonitor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      checks: {}
    };
  }

  async checkSiteStatus() {
    console.log('🔍 فحص سريع لحالة الموقع...');
    
    try {
      // فحص الصفحة الرئيسية
      await this.checkURL(SITE_URL);
      
      // فحص robots.txt
      await this.checkURL(`${SITE_URL}/robots.txt`);
      
      // فحص sitemap.xml
      await this.checkURL(`${SITE_URL}/sitemap.xml`);
      
      // فحص بعض الأدوات المهمة
      const importantTools = [
        '/tools/zakat-calculator',
        '/tools/date-converter',
        '/tools/currency-converter',
        '/tools/age-calculator'
      ];
      
      for (const tool of importantTools) {
        await this.checkURL(`${SITE_URL}${tool}`);
      }
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ خطأ في الفحص:', error.message);
    }
  }

  checkURL(url) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      https.get(url, (res) => {
        const responseTime = Date.now() - startTime;
        const status = res.statusCode;
        
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const result = {
            url,
            status,
            responseTime,
            size: data.length,
            hasTitle: data.includes('<title>'),
            hasMetaDescription: data.includes('meta name="description"'),
            hasH1: data.includes('<h1'),
            timestamp: new Date().toISOString()
          };
          
          this.results.checks[url] = result;
          
          // طباعة النتيجة فوراً
          const statusIcon = status === 200 ? '✅' : '❌';
          const speedIcon = responseTime < 1000 ? '⚡' : responseTime < 3000 ? '🟡' : '🔴';
          
          console.log(`${statusIcon} ${speedIcon} ${url}`);
          console.log(`   الحالة: ${status} | الوقت: ${responseTime}ms | الحجم: ${(result.size/1024).toFixed(1)}KB`);
          
          if (status !== 200) {
            console.log(`   ⚠️ مشكلة: كود الحالة ${status}`);
          }
          
          if (responseTime > 3000) {
            console.log(`   ⚠️ بطء: وقت الاستجابة ${responseTime}ms`);
          }
          
          if (!result.hasTitle && url === SITE_URL) {
            console.log(`   ⚠️ مفقود: عنوان الصفحة`);
          }
          
          if (!result.hasMetaDescription && url === SITE_URL) {
            console.log(`   ⚠️ مفقود: وصف الصفحة`);
          }
          
          console.log('');
          resolve(result);
        });
      }).on('error', (err) => {
        console.error(`❌ خطأ في ${url}:`, err.message);
        reject(err);
      });
    });
  }

  generateReport() {
    const report = {
      summary: {
        totalChecks: Object.keys(this.results.checks).length,
        successfulChecks: Object.values(this.results.checks).filter(c => c.status === 200).length,
        averageResponseTime: Math.round(
          Object.values(this.results.checks).reduce((sum, c) => sum + c.responseTime, 0) / 
          Object.keys(this.results.checks).length
        ),
        timestamp: this.results.timestamp
      },
      issues: [],
      recommendations: []
    };

    // تحليل المشاكل
    Object.values(this.results.checks).forEach(check => {
      if (check.status !== 200) {
        report.issues.push(`صفحة غير متاحة: ${check.url} (${check.status})`);
      }
      
      if (check.responseTime > 3000) {
        report.issues.push(`بطء في التحميل: ${check.url} (${check.responseTime}ms)`);
      }
      
      if (check.url === SITE_URL) {
        if (!check.hasTitle) {
          report.issues.push('الصفحة الرئيسية: عنوان مفقود');
        }
        if (!check.hasMetaDescription) {
          report.issues.push('الصفحة الرئيسية: وصف مفقود');
        }
        if (!check.hasH1) {
          report.issues.push('الصفحة الرئيسية: عنوان H1 مفقود');
        }
      }
    });

    // التوصيات
    if (report.summary.averageResponseTime > 2000) {
      report.recommendations.push('تحسين سرعة الموقع - متوسط وقت الاستجابة مرتفع');
    }
    
    if (report.issues.length > 0) {
      report.recommendations.push('إصلاح المشاكل المكتشفة فوراً');
      report.recommendations.push('فحص Google Search Console للمزيد من التفاصيل');
    }

    // حفظ التقرير
    const reportFile = `quick-seo-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    // طباعة الملخص
    console.log('📊 ملخص الفحص السريع:');
    console.log('================================');
    console.log(`✅ الفحوصات الناجحة: ${report.summary.successfulChecks}/${report.summary.totalChecks}`);
    console.log(`⚡ متوسط وقت الاستجابة: ${report.summary.averageResponseTime}ms`);
    console.log(`🕒 وقت الفحص: ${new Date(report.summary.timestamp).toLocaleString('ar-SA')}`);
    
    if (report.issues.length > 0) {
      console.log('\n⚠️ المشاكل المكتشفة:');
      report.issues.forEach(issue => console.log(`   • ${issue}`));
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 التوصيات:');
      report.recommendations.forEach(rec => console.log(`   • ${rec}`));
    }
    
    console.log(`\n📄 تقرير مفصل محفوظ في: ${reportFile}`);
    
    return report;
  }

  startContinuousMonitoring() {
    console.log('🔄 بدء المراقبة المستمرة...');
    console.log(`⏰ سيتم الفحص كل ${CHECK_INTERVAL / 60000} دقيقة`);
    
    // فحص فوري
    this.checkSiteStatus();
    
    // فحص دوري
    setInterval(() => {
      console.log('\n🔄 فحص دوري...');
      this.checkSiteStatus();
    }, CHECK_INTERVAL);
  }
}

// تشغيل الأداة
const monitor = new QuickSEOMonitor();

if (process.argv.includes('--continuous')) {
  monitor.startContinuousMonitoring();
} else {
  monitor.checkSiteStatus();
}
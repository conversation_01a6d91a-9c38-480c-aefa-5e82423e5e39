import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseKey);

export interface ToolRating {
  id: string;
  tool_slug: string;
  rating: number;
  comment?: string;
  created_at: string;
}

export interface ToolRatingStats {
  tool_slug: string;
  total_ratings: number;
  average_rating: number;
  rating_1_count: number;
  rating_2_count: number;
  rating_3_count: number;
  rating_4_count: number;
  rating_5_count: number;
}

export interface CreateRatingData {
  tool_slug: string;
  rating: number;
  comment?: string;
  user_ip?: string;
  user_session?: string;
}

// الحصول على إحصائيات التقييم لأداة معينة
export async function getToolRatingStats(toolSlug: string): Promise<ToolRatingStats | null> {
  try {
    const { data, error } = await supabase
      .from('tool_rating_stats')
      .select('*')
      .eq('tool_slug', toolSlug)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching rating stats:', error);
      return null;
    }

    return data || {
      tool_slug: toolSlug,
      total_ratings: 0,
      average_rating: 0,
      rating_1_count: 0,
      rating_2_count: 0,
      rating_3_count: 0,
      rating_4_count: 0,
      rating_5_count: 0,
    };
  } catch (error) {
    console.error('Error fetching rating stats:', error);
    return null;
  }
}

// إضافة تقييم جديد
export async function addToolRating(ratingData: CreateRatingData): Promise<boolean> {
  try {
    // الحصول على IP المستخدم وجلسة المتصفح
    const userSession = localStorage.getItem('user_session') || generateUserSession();
    localStorage.setItem('user_session', userSession);

    const { error } = await supabase
      .from('tool_ratings')
      .upsert({
        tool_slug: ratingData.tool_slug,
        rating: ratingData.rating,
        comment: ratingData.comment,
        user_session: userSession,
        user_ip: 'anonymous', // يمكن تحسينه لاحقاً للحصول على IP حقيقي
      }, {
        onConflict: 'tool_slug,user_ip,user_session'
      });

    if (error) {
      console.error('Error adding rating:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error adding rating:', error);
    return false;
  }
}

// الحصول على تقييمات أداة معينة
export async function getToolRatings(toolSlug: string, limit: number = 10): Promise<ToolRating[]> {
  try {
    const { data, error } = await supabase
      .from('tool_ratings')
      .select('*')
      .eq('tool_slug', toolSlug)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching ratings:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching ratings:', error);
    return [];
  }
}

// التحقق من وجود تقييم للمستخدم الحالي
export async function getUserRating(toolSlug: string): Promise<ToolRating | null> {
  try {
    const userSession = localStorage.getItem('user_session');
    if (!userSession) return null;

    const { data, error } = await supabase
      .from('tool_ratings')
      .select('*')
      .eq('tool_slug', toolSlug)
      .eq('user_session', userSession)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user rating:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error fetching user rating:', error);
    return null;
  }
}

// توليد معرف جلسة فريد
function generateUserSession(): string {
  return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// الحصول على جميع إحصائيات التقييمات
export async function getAllRatingStats(): Promise<ToolRatingStats[]> {
  try {
    const { data, error } = await supabase
      .from('tool_rating_stats')
      .select('*')
      .order('average_rating', { ascending: false });

    if (error) {
      console.error('Error fetching all rating stats:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching all rating stats:', error);
    return [];
  }
}

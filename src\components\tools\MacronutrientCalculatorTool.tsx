
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Beef, Wheat, Droplet, Flame } from 'lucide-react';
import { Label } from '@/components/ui/label';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  gender: z.enum(['male', 'female'], { required_error: 'الرجاء تحديد الجنس.' }),
  age: requiredNumber().int().min(1, 'العمر يجب أن يكون 1 أو أكثر.').max(120),
  weight: requiredNumber().positive('الوزن يجب أن يكون رقمًا موجبًا.'),
  height: requiredNumber().positive('الطول يجب أن يكون رقمًا موجبًا.'),
  activityLevel: z.string().min(1, 'الرجاء اختيار مستوى النشاط.'),
  goal: z.enum(['lose', 'maintain', 'gain']).default('maintain'),
});

interface Result {
  totalCalories: number;
  protein: number;
  carbs: number;
  fats: number;
}

const activityFactors: { [key: string]: { factor: number; label: string } } = {
  sedentary: { factor: 1.2, label: 'خامل (قليل أو بدون رياضة)' },
  light: { factor: 1.375, label: 'نشاط خفيف (رياضة 1-3 أيام/أسبوع)' },
  moderate: { factor: 1.55, label: 'نشاط متوسط (رياضة معتدلة 3-5 أيام/أسبوع)' },
  active: { factor: 1.725, label: 'نشيط (رياضة شديدة 6-7 أيام/أسبوع)' },
  veryActive: { factor: 1.9, label: 'نشيط جدًا (رياضي أو عمل بدني)' },
};

export function MacronutrientCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { gender, age, weight, height, activityLevel, goal } = data;
    
    // 1. Calculate BMR
    let bmr: number;
    if (gender === 'male') {
      bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
      bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }

    // 2. Calculate TDEE
    const tdee = bmr * activityFactors[activityLevel].factor;

    // 3. Adjust calories based on goal
    let totalCalories = tdee;
    if (goal === 'lose') {
      totalCalories -= 500;
    } else if (goal === 'gain') {
      totalCalories += 500;
    }

    // 4. Calculate Macronutrients (40% Carbs, 30% Protein, 30% Fats)
    const proteinCalories = totalCalories * 0.30;
    const carbsCalories = totalCalories * 0.40;
    const fatsCalories = totalCalories * 0.30;

    const proteinGrams = proteinCalories / 4;
    const carbsGrams = carbsCalories / 4;
    const fatsGrams = fatsCalories / 9;

    setResult({
      totalCalories: Math.round(totalCalories),
      protein: Math.round(proteinGrams),
      carbs: Math.round(carbsGrams),
      fats: Math.round(fatsGrams),
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة البروتين والكارب والدهون</CardTitle>
        <CardDescription>احسب احتياجك اليومي من السعرات والمغذيات الكبرى لتحقيق أهدافك الصحية.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="space-y-3 md:col-span-2">
                    <FormLabel>الجنس</FormLabel>
                    <FormControl className="flex flex-col items-end">
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4 pt-2"
                      >
                        <div className="">
                            <FormItem className="flex flex-row-reverse items-center space-x-2 space-x-reverse">
                              <FormControl><RadioGroupItem value="male" /></FormControl>
                              <FormLabel className="font-normal">ذكر</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-row-reverse items-center space-x-2 space-x-reverse">
                              <FormControl><RadioGroupItem value="female" /></FormControl>
                              <FormLabel className="font-normal">أنثى</FormLabel>
                            </FormItem>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField name="age" control={form.control} render={({ field }) => (<FormItem><FormLabel>العمر (بالسنوات)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField name="weight" control={form.control} render={({ field }) => (<FormItem><FormLabel>الوزن (كجم)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField name="height" control={form.control} render={({ field }) => (<FormItem><FormLabel>الطول (سم)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField
                control={form.control}
                name="activityLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>مستوى النشاط</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر مستوى نشاطك" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {Object.entries(activityFactors).map(([key, value]) => (
                          <SelectItem key={key} value={key}>{value.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="goal"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>هدفك الأساسي</FormLabel>
                    <FormControl>
                        <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="grid grid-cols-3 gap-4"
                        >
                            <Label className="border rounded-md p-3 flex items-center justify-center gap-2 cursor-pointer has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary">
                                <RadioGroupItem value="lose" /><span>خسارة الوزن</span>
                            </Label>
                            <Label className="border rounded-md p-3 flex items-center justify-center gap-2 cursor-pointer has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary">
                                <RadioGroupItem value="maintain" /><span>الحفاظ على الوزن</span>
                            </Label>
                            <Label className="border rounded-md p-3 flex items-center justify-center gap-2 cursor-pointer has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary">
                                <RadioGroupItem value="gain" /><span>زيادة الوزن</span>
                            </Label>
                        </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full">احسب احتياجك</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center space-y-4">
             <h3 className="text-xl font-headline font-semibold">النتائج التقديرية اليومية</h3>
             <div className="p-4 bg-primary/10 rounded-lg flex items-center justify-center gap-4 mb-4">
                 <Flame className="h-8 w-8 text-primary" />
                 <div>
                    <p className="text-sm text-muted-foreground">إجمالي السعرات الحرارية</p>
                    <p className="text-3xl font-bold font-mono text-primary">{result.totalCalories}</p>
                 </div>
             </div>
             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-secondary rounded-lg flex items-center gap-3">
                    <Beef className="h-8 w-8 text-red-500" />
                    <div><p className="text-sm text-muted-foreground">بروتين</p><p className="text-2xl font-bold font-mono">{result.protein} جرام</p></div>
                </div>
                <div className="p-4 bg-secondary rounded-lg flex items-center gap-3">
                    <Wheat className="h-8 w-8 text-yellow-500" />
                    <div><p className="text-sm text-muted-foreground">كربوهيدرات</p><p className="text-2xl font-bold font-mono">{result.carbs} جرام</p></div>
                </div>
                <div className="p-4 bg-secondary rounded-lg flex items-center gap-3">
                    <Droplet className="h-8 w-8 text-blue-500" />
                    <div><p className="text-sm text-muted-foreground">دهون</p><p className="text-2xl font-bold font-mono">{result.fats} جرام</p></div>
                </div>
             </div>
             <p className="text-xs text-muted-foreground pt-2">
                هذه الأرقام تقديرية بناءً على توزيع (40% كارب، 30% بروتين، 30% دهون). استشر مختصًا للحصول على توصيات شخصية.
             </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

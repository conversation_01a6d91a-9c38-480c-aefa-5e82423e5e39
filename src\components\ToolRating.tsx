'use client';

import { useState, useEffect } from 'react';
import { Star, MessageCircle, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  getToolRatingStats, 
  addToolRating, 
  getUserRating, 
  getToolRatings,
  type ToolRatingStats,
  type ToolRating 
} from '@/lib/ratings';

interface ToolRatingComponentProps {
  toolSlug: string;
  toolName: string;
}

export default function ToolRatingComponent({ toolSlug, toolName }: ToolRatingComponentProps) {
  const [stats, setStats] = useState<ToolRatingStats | null>(null);
  const [userRating, setUserRating] = useState<ToolRating | null>(null);
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [comment, setComment] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showCommentForm, setShowCommentForm] = useState<boolean>(false);
  const [recentRatings, setRecentRatings] = useState<ToolRating[]>([]);
  const [showAllRatings, setShowAllRatings] = useState<boolean>(false);

  useEffect(() => {
    loadRatingData();
  }, [toolSlug]);

  const loadRatingData = async () => {
    try {
      const [statsData, userRatingData, ratingsData] = await Promise.all([
        getToolRatingStats(toolSlug),
        getUserRating(toolSlug),
        getToolRatings(toolSlug, 5)
      ]);

      setStats(statsData);
      setUserRating(userRatingData);
      setRecentRatings(ratingsData);

      if (userRatingData) {
        setSelectedRating(userRatingData.rating);
        setComment(userRatingData.comment || '');
      }
    } catch (error) {
      console.error('Error loading rating data:', error);
    }
  };

  const handleRatingSubmit = async () => {
    if (selectedRating === 0) return;

    setIsSubmitting(true);
    try {
      const success = await addToolRating({
        tool_slug: toolSlug,
        rating: selectedRating,
        comment: comment.trim() || undefined,
      });

      if (success) {
        await loadRatingData();
        setShowCommentForm(false);
        // إظهار رسالة نجاح
      }
    } catch (error) {
      console.error('Error submitting rating:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (rating: number, interactive: boolean = false, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    return (
      <div className="flex items-center space-x-1 space-x-reverse">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            } ${interactive ? 'cursor-pointer hover:text-yellow-400 transition-colors' : ''}`}
            onClick={interactive ? () => setSelectedRating(star) : undefined}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!stats) {
    return (
      <div className="animate-pulse">
        <div className="h-32 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="text-lg font-semibold">تقييم الأداة</span>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Users className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{stats.total_ratings} تقييم</span>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* إحصائيات التقييم */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600">
              {stats.average_rating.toFixed(1)}
            </div>
            <div className="flex justify-center mt-1">
              {renderStars(Math.round(stats.average_rating), false, 'sm')}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              من {stats.total_ratings} تقييم
            </div>
          </div>
          
          <div className="flex-1 mx-6">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats[`rating_${rating}_count` as keyof ToolRatingStats] as number;
              const percentage = stats.total_ratings > 0 ? (count / stats.total_ratings) * 100 : 0;
              
              return (
                <div key={rating} className="flex items-center space-x-2 space-x-reverse mb-1">
                  <span className="text-sm w-8">{rating}</span>
                  <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-8">{count}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* نموذج التقييم */}
        {!userRating ? (
          <div className="space-y-4">
            <h3 className="font-medium">قيم هذه الأداة</h3>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="text-sm">تقييمك:</span>
              {renderStars(selectedRating, true, 'lg')}
            </div>

            {selectedRating > 0 && (
              <div className="space-y-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCommentForm(!showCommentForm)}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <MessageCircle className="h-4 w-4" />
                  <span>إضافة تعليق (اختياري)</span>
                </Button>

                {showCommentForm && (
                  <Textarea
                    placeholder="شاركنا رأيك في هذه الأداة..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="resize-none"
                    rows={3}
                  />
                )}

                <Button 
                  onClick={handleRatingSubmit}
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? 'جاري الإرسال...' : 'إرسال التقييم'}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-green-800 font-medium">شكراً لك على تقييمك!</span>
              <Badge variant="secondary">تم التقييم</Badge>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse mt-2">
              <span className="text-sm text-green-700">تقييمك:</span>
              {renderStars(userRating.rating, false, 'sm')}
            </div>
            {userRating.comment && (
              <p className="text-sm text-green-700 mt-2 italic">"{userRating.comment}"</p>
            )}
          </div>
        )}

        {/* التقييمات الأخيرة */}
        {recentRatings.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">آراء المستخدمين</h3>
              {recentRatings.length >= 5 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllRatings(!showAllRatings)}
                >
                  {showAllRatings ? 'إخفاء' : 'عرض المزيد'}
                </Button>
              )}
            </div>

            <div className="space-y-3">
              {(showAllRatings ? recentRatings : recentRatings.slice(0, 3)).map((rating) => (
                <div key={rating.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    {renderStars(rating.rating, false, 'sm')}
                    <span className="text-xs text-gray-500">
                      {formatDate(rating.created_at)}
                    </span>
                  </div>
                  {rating.comment && (
                    <p className="text-sm text-gray-700">{rating.comment}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

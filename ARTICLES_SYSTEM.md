# نظام إدارة المقالات - أدوات بالعربي (Supabase)

## نظرة عامة

تم إنشاء نظام شامل لإدارة المقالات باستخدام **Supabase** كقاعدة بيانات، يتيح للمديرين إنشاء وتحرير ونشر المقالات بسهولة، مع دعم كامل للغة العربية وتحسين محركات البحث.

## 🆕 التحديث الجديد: الانتقال إلى Supabase

تم الانتقال من Firebase إلى Supabase للاستفادة من:
- قاعدة بيانات PostgreSQL قوية ومرنة
- واجهة إدارة سهلة الاستخدام
- أداء أفضل للاستعلامات المعقدة
- دعم أفضل للبحث النصي باللغة العربية
- Row Level Security (RLS) للأمان المتقدم

## الميزات الرئيسية

### 1. إدارة المقالات
- ✅ إنشاء مقالات جديدة
- ✅ تحرير المقالات الموجودة
- ✅ حذف المقالات
- ✅ نظام المسودات والنشر
- ✅ المقالات المميزة
- ✅ إحصائيات المشاهدات والإعجابات

### 2. إدارة الفئات
- ✅ إنشاء فئات جديدة
- ✅ تنظيم المقالات حسب الفئات
- ✅ ترتيب الفئات

### 3. تحسين محركات البحث (SEO)
- ✅ عناوين SEO مخصصة
- ✅ أوصاف SEO
- ✅ كلمات مفتاحية
- ✅ روابط مختصرة (slugs)

### 4. المحتوى ذو الصلة
- ✅ ربط المقالات بالأدوات ذات الصلة
- ✅ اقتراح مقالات مشابهة
- ✅ نظام العلامات (tags)

## البنية التقنية

### قاعدة البيانات (Supabase PostgreSQL)

**معلومات المشروع:**
- اسم المشروع: `ادوات-بالعربي-مقالات`
- المعرف: `nhoclmaddmrkcyzrtubm`
- المنطقة: `eu-central-1`
- الرابط: `https://nhoclmaddmrkcyzrtubm.supabase.co`

#### جدول المقالات (articles)
```typescript
interface Article {
  id: string;
  title: string;
  slug: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  author: string;
  authorId: string;
  readTime: string;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  views: number;
  likes: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords: string[];
  relatedTools: string[];
  relatedArticles: string[];
  imageUrl?: string;
  imageAlt?: string;
}
```

#### جدول الفئات (categories)
```typescript
interface ArticleCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  icon?: string;
  order: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### API Endpoints

#### المقالات
- `GET /api/articles` - جلب قائمة المقالات مع فلترة
- `POST /api/articles` - إنشاء مقال جديد
- `GET /api/articles/[id]` - جلب مقال واحد
- `PUT /api/articles/[id]` - تحديث مقال
- `DELETE /api/articles/[id]` - حذف مقال
- `PATCH /api/articles/[id]?action=like` - إضافة إعجاب
- `PATCH /api/articles/[id]?action=view` - تسجيل مشاهدة

#### الفئات
- `GET /api/categories` - جلب قائمة الفئات
- `POST /api/categories` - إنشاء فئة جديدة

#### الإحصائيات
- `GET /api/articles/stats` - جلب إحصائيات المقالات

### الصفحات

#### صفحات المستخدمين
- `/articles` - قائمة المقالات المنشورة
- `/articles/[slug]` - عرض مقال واحد

#### صفحات الإدارة
- `/admin/articles` - لوحة إدارة المقالات
- `/admin/articles/new` - إنشاء مقال جديد
- `/admin/articles/[id]/edit` - تحرير مقال

## الملفات المهمة

### النماذج والأنواع
- `src/types/article.ts` - تعريف أنواع البيانات
- `src/lib/articles-supabase.ts` - دوال إدارة المقالات
- `src/lib/supabase.ts` - إعداد Supabase

### API Routes
- `src/app/api/articles/route.ts` - API المقالات الرئيسي
- `src/app/api/articles/[id]/route.ts` - API مقال واحد
- `src/app/api/categories/route.ts` - API الفئات
- `src/app/api/articles/stats/route.ts` - API الإحصائيات

### صفحات المستخدمين
- `src/app/articles/page.tsx` - قائمة المقالات
- `src/app/articles/[slug]/page.tsx` - عرض مقال واحد

### صفحات الإدارة
- `src/app/admin/articles/page.tsx` - لوحة إدارة المقالات
- `src/app/admin/articles/new/page.tsx` - إنشاء مقال جديد
- `src/app/admin/articles/[id]/edit/page.tsx` - تحرير مقال

### إعداد Supabase

#### متغيرات البيئة المطلوبة
```env
NEXT_PUBLIC_SUPABASE_URL=https://nhoclmaddmrkcyzrtubm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### الدوال المساعدة في قاعدة البيانات
```sql
-- دالة لزيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_views(article_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE articles
    SET views = views + 1, updated_at = NOW()
    WHERE id = article_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة لزيادة عدد الإعجابات
CREATE OR REPLACE FUNCTION increment_likes(article_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE articles
    SET likes = likes + 1, updated_at = NOW()
    WHERE id = article_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### الملفات المهمة
- `src/lib/supabase.ts` - إعداد Supabase
- `src/lib/articles-supabase.ts` - دوال إدارة المقالات
- `src/types/article.ts` - تعريف أنواع البيانات

## الاستخدام

### إنشاء مقال جديد
1. انتقل إلى `/admin/articles`
2. اضغط على "مقال جديد"
3. املأ البيانات المطلوبة
4. احفظ كمسودة أو انشر مباشرة

### تحرير مقال
1. من لوحة إدارة المقالات، اضغط على أيقونة التحرير
2. عدل البيانات المطلوبة
3. احفظ التغييرات

### إدارة الفئات
1. استخدم API الفئات لإنشاء فئات جديدة
2. ربط المقالات بالفئات المناسبة

## الأمان

### قواعد Row Level Security (RLS)
- المقالات المنشورة متاحة للقراءة للجميع
- المقالات غير المنشورة متاحة للمديرين فقط
- الكتابة متاحة للمديرين فقط
- إمكانية تحديث المشاهدات والإعجابات للجميع

### قواعد التخزين
- رفع الصور متاح للمديرين فقط
- حد أقصى 5MB للصور
- أنواع ملفات الصور فقط مسموحة

## التطوير المستقبلي

### ميزات مقترحة
- [ ] محرر نصوص متقدم (WYSIWYG)
- [ ] رفع الصور مباشرة
- [ ] نظام التعليقات
- [ ] جدولة النشر
- [ ] إشعارات البريد الإلكتروني
- [ ] تصدير المقالات
- [ ] نسخ احتياطية تلقائية
- [ ] تحليلات متقدمة
- [ ] دعم اللغات المتعددة
- [ ] نظام الموافقة على المقالات

### تحسينات تقنية
- [ ] تحسين الأداء مع التخزين المؤقت
- [ ] بحث متقدم مع Algolia
- [ ] ضغط الصور التلقائي
- [ ] CDN للمحتوى الثابت
- [ ] اختبارات تلقائية
- [ ] مراقبة الأخطاء

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

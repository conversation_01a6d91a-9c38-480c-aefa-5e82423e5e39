
'use server';

/**
 * @fileOverview Suggests gifts based on user criteria using AI.
 * 
 * - suggestGifts - A function that provides gift suggestions.
 * - GiftSuggestionInput - The input type for the suggestGifts function.
 * - GiftSuggestionOutput - The return type for the suggestGifts function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const GiftSuggestionInputSchema = z.object({
  occasion: z.string().describe('المناسبة، مثل "عيد ميلاد" أو "تخرج".'),
  occasionDescription: z.string().optional().describe('وصف إضافي للمناسبة.'),
  personDescription: z.string().optional().describe('وصف للشخص الذي ستقدم له الهدية (اهتماماته، طباعه).'),
  ageGroup: z.string().describe('الفئة العمرية للشخص، مثل "شاب (20-30)" أو "طفل".'),
  gender: z.enum(['male', 'female']).describe('جنس الشخص (ذكر أو أنثى).'),
  budget: z.string().describe('الميزانية التقريبية للهدية، مثل "متوسطة (100-500 ريال)".'),
});
export type GiftSuggestionInput = z.infer<typeof GiftSuggestionInputSchema>;

const GiftSuggestionSchema = z.object({
    name: z.string().describe('اسم الهدية المقترحة بشكل جذاب ومختصر.'),
    description: z.string().describe('وصف قصير ومقنع للهدية وسبب كونها مناسبة.'),
});
export type GiftSuggestion = z.infer<typeof GiftSuggestionSchema>;

const GiftSuggestionOutputSchema = z.object({
  suggestions: z.array(GiftSuggestionSchema).length(5).describe('قائمة من 5 اقتراحات هدايا فريدة.'),
});
export type GiftSuggestionOutput = z.infer<typeof GiftSuggestionOutputSchema>;


export async function suggestGifts(input: GiftSuggestionInput): Promise<GiftSuggestionOutput> {
  return suggestGiftsFlow(input);
}

const suggestGiftsPrompt = ai.definePrompt({
  name: 'suggestGiftsPrompt',
  input: { schema: GiftSuggestionInputSchema },
  output: { schema: GiftSuggestionOutputSchema },
  prompt: `
    أنت خبير هدايا متخصص في الثقافة العربية والخليجية، ومهمتك هي اقتراح 5 هدايا واقعية وعملية يمكن شراؤها وتقديمها في المنطقة العربية.
    
    استخدم المعايير التالية لتقديم اقتراحاتك:
    - المناسبة الرئيسية: {{{occasion}}}
    - الفئة العمرية: {{{ageGroup}}}
    - الجنس: {{{gender}}}
    - الميزانية: {{{budget}}}
    {{#if occasionDescription}}
    - وصف إضافي للمناسبة من المستخدم: {{{occasionDescription}}}
    {{/if}}
    {{#if personDescription}}
    - وصف للشخص من المستخدم (اهتماماته وطباعه): {{{personDescription}}}
    {{/if}}

    يجب أن تكون الاقتراحات مناسبة ثقافيًا وواقعية. قدم وصفًا مقنعًا لكل هدية يوضح لماذا هي خيار رائع.
  `,
});

const suggestGiftsFlow = ai.defineFlow(
  {
    name: 'suggestGiftsFlow',
    inputSchema: GiftSuggestionInputSchema,
    outputSchema: GiftSuggestionOutputSchema,
  },
  async (input) => {
    const { output } = await suggestGiftsPrompt(input);
    return output!;
  }
);

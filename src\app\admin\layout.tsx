'use client';

import { usePathname } from 'next/navigation';
import { AuthProvider } from '@/components/AuthProvider';
import { AdminProtection } from '@/components/AdminProtection';
import { AdminHeader } from '@/components/AdminHeader';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isLoginPage = pathname === '/admin/login';

  return (
    <AuthProvider>
      {isLoginPage ? (
        children
      ) : (
        <AdminProtection>
          <div className="min-h-screen bg-gray-50">
            <AdminHeader />
            <main className="container mx-auto px-4 py-8">
              {children}
            </main>
          </div>
        </AdminProtection>
      )}
    </AuthProvider>
  );
}

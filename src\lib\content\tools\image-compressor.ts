
export default {
  seoDescription: `
    <div class="prose prose-lg max-w-none">
      <h1>أداة ضغط الصور: قلل حجم الصور مع الحفاظ على الجودة</h1>
      <p>في عالم الويب والتواصل الرقمي، يعد حجم الصور عاملاً حاسماً في سرعة تحميل المواقع وتجربة المستخدم. الصور الكبيرة يمكن أن تبطئ موقعك بشكل كبير. تقدم <strong>أداة ضغط الصور</strong> حلاً مجانيًا وآمنًا يسمح لك بتقليل حجم ملفات الصور (JPG و PNG) مع التحكم الكامل في مستوى الجودة.</p>
      
      <h2>لماذا تحتاج إلى ضغط صورك؟</h2>
      <ul>
        <li><strong>سرعة الموقع:</strong> الصور الأصغر حجمًا تعني تحميل أسرع للصفحات، وهو أمر حيوي لتحسين محركات البحث (SEO) ورضا الزوار.</li>
        <li><strong>توفير مساحة التخزين:</strong> تقليل حجم الصور يوفر مساحة على خوادم الاستضافة أو على جهازك الشخصي.</li>
        <li><strong>سهولة المشاركة:</strong> إرسال الصور المضغوطة عبر البريد الإلكتروني أو تطبيقات المراسلة يكون أسرع وأسهل.</li>
      </ul>

      <h2>كيفية الاستخدام</h2>
      <ol>
        <li><strong>اختر صورتك:</strong> انقر لاختيار صورة بصيغة JPG أو PNG من جهازك.</li>
        <li><strong>حدد مستوى الجودة:</strong> استخدم شريط التمرير لاختيار مستوى الجودة المطلوب. جودة أعلى تعني حجم ملف أكبر، والعكس صحيح.</li>
        <li><strong>ابدأ الضغط:</strong> انقر على زر "ضغط الصورة".</li>
        <li><strong>قارن وحمّل:</strong> ستظهر الصورة الأصلية بجانب الصورة المضغوطة مع حجم كل منهما. إذا كنت راضيًا عن النتيجة، قم بتحميل الصورة الجديدة.</li>
      </ol>

      <h3>الخصوصية أولاً</h3>
      <p>نحن نقدر خصوصيتك. تتم جميع عمليات معالجة وضغط الصور <strong>محليًا في متصفحك</strong>. لا يتم رفع صورك إلى أي خادم، مما يضمن بقاءها آمنة وخاصة بك تمامًا.</p>
    </div>
  `,
  faq: [
    {
      question: 'ما الفرق بين ضغط JPG و PNG؟',
      answer: 'ضغط JPG (Lossy) يتخلص من بعض البيانات لتقليل الحجم بشكل كبير، وهو مثالي للصور الفوتوغرافية. ضغط PNG (Lossless) يحافظ على كل البيانات ولكنه لا يقلل الحجم بنفس القدر، وهو مثالي للشعارات والرسومات التي تحتاج إلى خلفية شفافة.'
    },
    {
      question: 'ما هو أفضل مستوى جودة للاستخدام؟',
      answer: 'للاستخدام على الويب، غالبًا ما يكون مستوى الجودة بين 70% و 85% هو التوازن المثالي بين الجودة وحجم الملف. يمكنك تجربة مستويات مختلفة في أداتنا لرؤية الفرق بنفسك.'
    },
    {
      question: 'هل يؤثر الضغط على أبعاد الصورة؟',
      answer: 'لا، عملية الضغط تقلل من حجم الملف (بالكيلوبايت أو الميجابايت) ولكنها لا تغير أبعاد الصورة (الطول والعرض بالبكسل).'
    },
    {
      question: 'هل يمكنني ضغط أنواع أخرى من الصور مثل WebP أو GIF؟',
      answer: 'حاليًا، تركز الأداة على الصيغتين الأكثر شيوعًا وهما JPG و PNG. قد نضيف دعمًا لصيغ أخرى في المستقبل.'
    }
  ]
};

#!/usr/bin/env node

/**
 * أداة فحص SEO شاملة لموقع أدوات بالعربي
 * تفحص جميع جوانب SEO وتقدم تقرير مفصل
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

class SEOChecker {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.results = {
      technical: {},
      content: {},
      performance: {},
      accessibility: {},
      issues: [],
      recommendations: []
    };
  }

  async checkSitemap() {
    console.log('🔍 فحص sitemap.xml...');
    try {
      const sitemapUrl = `${this.baseUrl}/sitemap.xml`;
      const response = await this.fetchUrl(sitemapUrl);
      
      if (response.statusCode === 200) {
        this.results.technical.sitemap = 'موجود ويعمل';
        console.log('✅ sitemap.xml موجود ويعمل بشكل صحيح');
        
        // فحص محتوى sitemap
        const urlCount = (response.body.match(/<url>/g) || []).length;
        console.log(`📊 عدد الصفحات في sitemap: ${urlCount}`);
        this.results.technical.sitemapUrls = urlCount;
      } else {
        this.results.technical.sitemap = 'غير موجود أو لا يعمل';
        this.results.issues.push('sitemap.xml غير متاح');
        console.log('❌ sitemap.xml غير متاح');
      }
    } catch (error) {
      this.results.issues.push(`خطأ في فحص sitemap: ${error.message}`);
      console.log('❌ خطأ في فحص sitemap:', error.message);
    }
  }

  async checkRobots() {
    console.log('🔍 فحص robots.txt...');
    try {
      const robotsUrl = `${this.baseUrl}/robots.txt`;
      const response = await this.fetchUrl(robotsUrl);
      
      if (response.statusCode === 200) {
        this.results.technical.robots = 'موجود ويعمل';
        console.log('✅ robots.txt موجود ويعمل بشكل صحيح');
        
        // فحص محتوى robots.txt
        const body = response.body.toLowerCase();
        if (body.includes('sitemap:')) {
          console.log('✅ robots.txt يحتوي على رابط sitemap');
        } else {
          this.results.issues.push('robots.txt لا يحتوي على رابط sitemap');
        }
        
        if (body.includes('disallow: /')) {
          this.results.issues.push('robots.txt قد يمنع فهرسة الموقع');
        }
      } else {
        this.results.technical.robots = 'غير موجود';
        this.results.issues.push('robots.txt غير متاح');
        console.log('❌ robots.txt غير متاح');
      }
    } catch (error) {
      this.results.issues.push(`خطأ في فحص robots: ${error.message}`);
      console.log('❌ خطأ في فحص robots:', error.message);
    }
  }

  async checkHomePage() {
    console.log('🔍 فحص الصفحة الرئيسية...');
    try {
      const response = await this.fetchUrl(this.baseUrl);
      
      if (response.statusCode === 200) {
        const html = response.body;
        
        // فحص title
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
          const title = titleMatch[1].trim();
          console.log(`📝 العنوان: ${title}`);
          this.results.content.title = title;
          
          if (title.length < 30) {
            this.results.issues.push('العنوان قصير جداً (أقل من 30 حرف)');
          } else if (title.length > 60) {
            this.results.issues.push('العنوان طويل جداً (أكثر من 60 حرف)');
          }
        } else {
          this.results.issues.push('لا يوجد عنوان في الصفحة الرئيسية');
        }
        
        // فحص meta description
        const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
        if (descMatch) {
          const description = descMatch[1].trim();
          console.log(`📄 الوصف: ${description.substring(0, 100)}...`);
          this.results.content.description = description;
          
          if (description.length < 120) {
            this.results.issues.push('الوصف قصير جداً (أقل من 120 حرف)');
          } else if (description.length > 160) {
            this.results.issues.push('الوصف طويل جداً (أكثر من 160 حرف)');
          }
        } else {
          this.results.issues.push('لا يوجد وصف meta في الصفحة الرئيسية');
        }
        
        // فحص h1
        const h1Match = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
        if (h1Match) {
          console.log(`🏷️ العنوان الرئيسي H1: ${h1Match[1].trim()}`);
          this.results.content.h1 = h1Match[1].trim();
        } else {
          this.results.issues.push('لا يوجد عنوان H1 في الصفحة الرئيسية');
        }
        
        // فحص structured data
        if (html.includes('application/ld+json')) {
          console.log('✅ يحتوي على structured data');
          this.results.technical.structuredData = true;
        } else {
          this.results.issues.push('لا يحتوي على structured data');
          this.results.technical.structuredData = false;
        }
        
        // فحص Open Graph
        if (html.includes('og:title') && html.includes('og:description')) {
          console.log('✅ يحتوي على Open Graph tags');
          this.results.technical.openGraph = true;
        } else {
          this.results.issues.push('لا يحتوي على Open Graph tags كاملة');
          this.results.technical.openGraph = false;
        }
        
      } else {
        this.results.issues.push(`الصفحة الرئيسية غير متاحة (كود: ${response.statusCode})`);
      }
    } catch (error) {
      this.results.issues.push(`خطأ في فحص الصفحة الرئيسية: ${error.message}`);
      console.log('❌ خطأ في فحص الصفحة الرئيسية:', error.message);
    }
  }

  async checkSSL() {
    console.log('🔍 فحص شهادة SSL...');
    try {
      if (this.baseUrl.startsWith('https://')) {
        console.log('✅ الموقع يستخدم HTTPS');
        this.results.technical.ssl = true;
      } else {
        this.results.issues.push('الموقع لا يستخدم HTTPS');
        this.results.technical.ssl = false;
      }
    } catch (error) {
      this.results.issues.push(`خطأ في فحص SSL: ${error.message}`);
    }
  }

  async fetchUrl(url) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const req = client.get(url, {
        headers: {
          'User-Agent': 'SEO-Checker/1.0'
        }
      }, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        });
      });
      
      req.on('error', reject);
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('انتهت مهلة الاتصال'));
      });
    });
  }

  generateRecommendations() {
    console.log('\n📋 توليد التوصيات...');
    
    if (this.results.issues.length === 0) {
      this.results.recommendations.push('ممتاز! لا توجد مشاكل تقنية واضحة');
    }
    
    if (!this.results.technical.structuredData) {
      this.results.recommendations.push('أضف structured data (JSON-LD) لتحسين فهم محركات البحث للمحتوى');
    }
    
    if (!this.results.technical.openGraph) {
      this.results.recommendations.push('أضف Open Graph tags لتحسين المشاركة على وسائل التواصل');
    }
    
    if (this.results.technical.sitemapUrls < 10) {
      this.results.recommendations.push('أضف المزيد من الصفحات إلى sitemap.xml');
    }
    
    this.results.recommendations.push('تحقق من Google Search Console للحصول على معلومات مفصلة');
    this.results.recommendations.push('استخدم Google PageSpeed Insights لفحص سرعة الموقع');
    this.results.recommendations.push('تحقق من Mobile-Friendly Test من Google');
  }

  async runFullCheck() {
    console.log(`🚀 بدء فحص SEO شامل للموقع: ${this.baseUrl}\n`);
    
    await this.checkSSL();
    await this.checkRobots();
    await this.checkSitemap();
    await this.checkHomePage();
    
    this.generateRecommendations();
    
    console.log('\n📊 تقرير SEO النهائي:');
    console.log('='.repeat(50));
    
    console.log('\n🔧 الجوانب التقنية:');
    Object.entries(this.results.technical).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    
    console.log('\n📝 المحتوى:');
    Object.entries(this.results.content).forEach(([key, value]) => {
      console.log(`  ${key}: ${typeof value === 'string' ? value.substring(0, 100) + '...' : value}`);
    });
    
    if (this.results.issues.length > 0) {
      console.log('\n⚠️ المشاكل المكتشفة:');
      this.results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    console.log('\n💡 التوصيات:');
    this.results.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ انتهى فحص SEO');
    
    return this.results;
  }
}

// تشغيل الفحص
if (require.main === module) {
  const baseUrl = process.argv[2] || 'https://adawat.org';
  const checker = new SEOChecker(baseUrl);
  checker.runFullCheck().catch(console.error);
}

module.exports = SEOChecker;

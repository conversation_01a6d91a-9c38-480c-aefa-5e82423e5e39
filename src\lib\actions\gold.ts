
'use server';

import { COUNTRIES_CURRENCIES, COUNTRY_CODE_TO_CURRENCY } from '@/lib/constants/currencies';
import { getIpInfo } from './ip';

interface PriceData {
    perOunce: number;
    perGram: number;
    perKilo: number;
    perPound?: number; // Added for copper
}

interface FullPriceData {
    success: boolean;
    timestamp: number;
    selectedCurrency: string;
    countryInfo: (typeof COUNTRIES_CURRENCIES)[keyof typeof COUNTRIES_CURRENCIES];
    prices: {
        usd: PriceData;
        local: PriceData;
    };
    source: string;
    metalName: string;
    metalSymbol: string;
    change: number;
    changePercent: number;
    open: number;
    high: number;
    low: number;
    volume: string;
    error?: string;
    karats?: { [key: string]: number };
}

async function getExchangeRate(currency: string): Promise<number> {
  if (currency === 'USD') return 1;
  try {
    const response = await fetch(`https://open.er-api.com/v6/latest/USD`, {
      next: { revalidate: 3600 },
      headers: { 'User-Agent': 'Mozilla/5.0 (compatible; Metal Price Tool)' },
    });
    if (response.ok) {
      const data = await response.json();
      if (data && data.rates && data.rates[currency]) {
        return parseFloat(data.rates[currency]);
      }
    }
  } catch (error) {
    console.error(`ExchangeRate-API Error for ${currency}:`, error);
  }
  const fallbackRates: Record<string, number> = { SAR: 3.75, AED: 3.67, KWD: 0.31, BHD: 0.38, OMR: 0.38, QAR: 3.64, EGP: 49.50, JOD: 0.71, EUR: 0.92, USD: 1.0 };
  return fallbackRates[currency] || 1;
}

const METAL_YAHOO_SYMBOLS = {
  XAU: 'GC=F', // Gold
  XAG: 'SI=F', // Silver
  XCU: 'HG=F'  // Copper
} as const;


function formatVolume(volume?: number): string {
    if (!volume) return 'N/A';
    if (volume >= 1000000000) return `${(volume / 1000000000).toFixed(2)}B`;
    if (volume >= 1000000) return `${(volume / 1000000).toFixed(2)}M`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(2)}K`;
    return volume.toString();
}

async function fetchMetalPrice(metalSymbol: 'XAU' | 'XAG' | 'XCU'): Promise<{ price: number, timestamp: number, open: number, high: number, low: number, change: number, changePercent: number, volume: number, source: string } | null> {
    try {
        const yahooSymbol = METAL_YAHOO_SYMBOLS[metalSymbol];
        const response = await fetch(`https://query1.finance.yahoo.com/v8/finance/chart/${yahooSymbol}`, {
            next: { revalidate: 60 },
            headers: { 'User-Agent': 'Mozilla/5.0 (compatible; Metal Price Tool)' },
        });

        if (response.ok) {
            const data = await response.json();
            const result = data.chart?.result?.[0];
            if (result && result.meta) {
                const meta = result.meta;
                const price = meta.regularMarketPrice || meta.previousClose || 0;
                const previousClose = meta.previousClose || price;
                
                const change = meta.regularMarketChange ?? (price - previousClose);
                const changePercent = meta.regularMarketChangePercent ?? (previousClose !== 0 ? (change / previousClose) * 100 : 0);

                return {
                    price,
                    timestamp: meta.regularMarketTime,
                    open: meta.regularMarketOpen || price,
                    high: meta.regularMarketDayHigh || price,
                    low: meta.regularMarketDayLow || price,
                    change,
                    changePercent,
                    volume: meta.regularMarketVolume,
                    source: 'Yahoo Finance'
                };
            }
        }
    } catch (e) {
        console.error(`Error fetching from Yahoo Finance for ${metalSymbol}:`, e);
    }
    return null;
}

async function getMetalPrices(metalSymbol: 'XAU' | 'XAG' | 'XCU', metalName: string, requestedCurrency?: string): Promise<Omit<FullPriceData, 'karats'>> {
  let selectedCurrency = 'SAR';
  let ipError;

  if (requestedCurrency && COUNTRIES_CURRENCIES[requestedCurrency as keyof typeof COUNTRIES_CURRENCIES]) {
    selectedCurrency = requestedCurrency;
  } else {
    const ipInfo = await getIpInfo();
    ipError = ipInfo.error;
    if (ipInfo.countryCode && COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode]) {
      selectedCurrency = COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode];
    }
  }
  
  const exchangeRate = await getExchangeRate(selectedCurrency);
  const metalData = await fetchMetalPrice(metalSymbol);
  
  if (!metalData) {
      return {
          success: false,
          timestamp: Date.now() / 1000,
          selectedCurrency,
          countryInfo: COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES],
          prices: { usd: { perOunce: 0, perGram: 0, perKilo: 0 }, local: { perOunce: 0, perGram: 0, perKilo: 0 } },
          source: 'N/A',
          metalName,
          metalSymbol,
          error: `فشل تحميل أسعار ${metalName}.`,
          change: 0, changePercent: 0, open: 0, high: 0, low: 0, volume: 'N/A',
      };
  }

  const priceUSD = metalData.price;
  const source = metalData.source;

  const processMetal = (priceUSD: number) => {
    let pricePerGramUSD: number;
    let mainUnitUSD: number = priceUSD;

    if (metalSymbol === 'XAU' || metalSymbol === 'XAG') {
        // Gold and Silver price is per troy ounce
        pricePerGramUSD = priceUSD / 31.1035;
    } else { // XCU for Copper
        // Copper price is per pound
        pricePerGramUSD = priceUSD / 453.592;
    }
    
    const priceLocal = priceUSD * exchangeRate;
    const pricePerGramLocal = pricePerGramUSD * exchangeRate;
    const pricePerKiloUSD = pricePerGramUSD * 1000;
    const pricePerKiloLocal = pricePerGramLocal * 1000;

    let prices: { usd: PriceData, local: PriceData };

    if (metalSymbol === 'XCU') {
      prices = {
        usd: { perPound: mainUnitUSD, perGram: pricePerGramUSD, perKilo: pricePerKiloUSD, perOunce: 0 },
        local: { perPound: priceLocal, perGram: pricePerGramLocal, perKilo: pricePerKiloLocal, perOunce: 0 },
      };
    } else {
      prices = {
        usd: { perOunce: mainUnitUSD, perGram: pricePerGramUSD, perKilo: pricePerKiloUSD },
        local: { perOunce: priceLocal, perGram: pricePerGramLocal, perKilo: pricePerKiloLocal },
      };
    }

    return prices;
  };

  const fullPriceData = processMetal(priceUSD);

  return {
    success: true,
    timestamp: metalData.timestamp,
    selectedCurrency,
    countryInfo: COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES],
    prices: fullPriceData,
    source,
    metalName,
    metalSymbol,
    change: metalData.change,
    changePercent: metalData.changePercent,
    open: metalData.open,
    high: metalData.high,
    low: metalData.low,
    volume: formatVolume(metalData.volume),
    error: ipError,
  };
}

export async function getGoldPrice(requestedCurrency?: string): Promise<FullPriceData> {
    const data = await getMetalPrices('XAU', 'الذهب', requestedCurrency);
    if (!data.success) return { ...data, karats: {} };

    const karats = {
        '24': data.prices.local.perGram, 
        '22': data.prices.local.perGram * (22/24),
        '21': data.prices.local.perGram * (21/24), 
        '18': data.prices.local.perGram * (18/24),
    };
    return { ...data, karats };
}

export async function getSilverPrice(requestedCurrency?: string) {
    return getMetalPrices('XAG', 'الفضة', requestedCurrency);
}

export async function getCopperPrice(requestedCurrency?: string) {
    return getMetalPrices('XCU', 'النحاس', requestedCurrency);
}

export async function getGoldAndSilverPrices(requestedCurrency?: string) {
    const [goldData, silverData] = await Promise.all([
        getGoldPrice(requestedCurrency),
        getSilverPrice(requestedCurrency)
    ]);

    return {
        success: goldData.success && silverData.success,
        timestamp: goldData.timestamp,
        selectedCurrency: goldData.selectedCurrency,
        countryInfo: goldData.countryInfo,
        prices: {
            gold: goldData.prices,
            silver: silverData.prices,
        },
        source: goldData.source,
        error: goldData.error || silverData.error,
    };
}

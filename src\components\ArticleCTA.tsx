'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Star, Heart, Share2, Check } from 'lucide-react';

interface ArticleCTAProps {
  title?: string;
  description?: string;
}

export function ArticleCTA({
  title = "هل استفدت من هذه المقالة؟",
  description = "ساعدنا في نشر المعرفة وشارك المقالة مع الآخرين"
}: ArticleCTAProps) {
  const [isShared, setIsShared] = useState(false);
  return (
    <Card className="mt-8 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
      <CardContent className="p-6 text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-green-100 rounded-full">
            <Heart className="h-8 w-8 text-green-600" />
          </div>
        </div>
        
        <h3 className="text-xl font-bold text-green-800 mb-2">{title}</h3>
        <p className="text-green-700 mb-6 max-w-md mx-auto">
          {description}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center max-w-lg mx-auto">
          <Button
            className={`transition-all duration-300 ${
              isShared
                ? 'bg-green-700 hover:bg-green-800 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
            onClick={async () => {
              try {
                if (navigator.share) {
                  await navigator.share({
                    title: document.title,
                    text: 'مقالة مفيدة من جامع الأدوات',
                    url: window.location.href
                  });
                  setIsShared(true);
                  setTimeout(() => setIsShared(false), 3000);
                } else if (navigator.clipboard) {
                  await navigator.clipboard.writeText(window.location.href);
                  setIsShared(true);
                  setTimeout(() => setIsShared(false), 3000);
                } else {
                  // fallback للمتصفحات القديمة
                  const textArea = document.createElement('textarea');
                  textArea.value = window.location.href;
                  document.body.appendChild(textArea);
                  textArea.select();
                  document.execCommand('copy');
                  document.body.removeChild(textArea);
                  setIsShared(true);
                  setTimeout(() => setIsShared(false), 3000);
                }
              } catch (error) {
                console.error('خطأ في المشاركة:', error);
              }
            }}
          >
            {isShared ? (
              <>
                <Check className="h-4 w-4 ml-2" />
                تم النسخ!
              </>
            ) : (
              <>
                <Share2 className="h-4 w-4 ml-2" />
                شارك المقالة
              </>
            )}
          </Button>
          
          <Link href="/tools">
            <Button variant="outline" className="border-green-300 text-green-700 hover:bg-green-50">
              <ArrowLeft className="h-4 w-4 ml-2" />
              استكشف الأدوات
            </Button>
          </Link>
          
          <Link href="/articles">
            <Button variant="outline" className="border-green-300 text-green-700 hover:bg-green-50">
              <Star className="h-4 w-4 ml-2" />
              المزيد من المقالات
            </Button>
          </Link>
        </div>
        
        <div className="mt-6 pt-4 border-t border-green-200">
          <p className="text-sm text-green-600">
            💡 نصيحة: احفظ هذه الصفحة في المفضلة للرجوع إليها لاحقاً
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

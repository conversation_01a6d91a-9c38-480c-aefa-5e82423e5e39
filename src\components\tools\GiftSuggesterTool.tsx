
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Gift, Lightbulb, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { suggestGifts, GiftSuggestion } from '@/ai/flows/suggest-gifts-flow';
import { Textarea } from '../ui/textarea';

const FormSchema = z.object({
  occasion: z.string({ required_error: 'الرجاء اختيار المناسبة.' }),
  occasionDescription: z.string().optional(),
  personDescription: z.string().optional(),
  ageGroup: z.string({ required_error: 'الرجاء اختيار الفئة العمرية.' }),
  gender: z.enum(['male', 'female'], { required_error: 'الرجاء تحديد الجنس.' }),
  budget: z.string({ required_error: 'الرجاء تحديد الميزانية.' }),
});

type FormValues = z.infer<typeof FormSchema>;

export function GiftSuggesterTool() {
  const [suggestions, setSuggestions] = useState<GiftSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    setSuggestions([]);
    try {
      const result = await suggestGifts(data);
      setSuggestions(result.suggestions);
    } catch (error) {
      console.error("AI gift suggestion error:", error);
      toast({
        variant: 'destructive',
        title: 'حدث خطأ',
        description: 'لم نتمكن من الحصول على اقتراحات في الوقت الحالي. يرجى المحاولة مرة أخرى.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حاسبة الهدايا الذكية</CardTitle>
        <CardDescription>
          في حيرة من أمرك؟ دع الذكاء الاصطناعي يساعدك في العثور على الهدية المثالية.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="occasion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>المناسبة</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر المناسبة" /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="eid">عيد (فطر/أضحى)</SelectItem>
                        <SelectItem value="birthday">عيد ميلاد</SelectItem>
                        <SelectItem value="wedding">زواج</SelectItem>
                        <SelectItem value="graduation">تخرج</SelectItem>
                        <SelectItem value="promotion">ترقية وظيفية</SelectItem>
                        <SelectItem value="new_baby">مولود جديد</SelectItem>
                        <SelectItem value="new_home">منزل جديد</SelectItem>
                        <SelectItem value="get_well">زيارة مريض</SelectItem>
                        <SelectItem value="hajj_umrah_return">عودة من الحج/العمرة</SelectItem>
                        <SelectItem value="general_visit">زيارة عامة</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="ageGroup"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>عمر المُهدى إليه</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر الفئة العمرية" /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="child">طفل (أقل من 12)</SelectItem>
                        <SelectItem value="teen">مراهق (13-19)</SelectItem>
                        <SelectItem value="young_adult">شاب (20-30)</SelectItem>
                        <SelectItem value="adult">بالغ (31-50)</SelectItem>
                        <SelectItem value="senior">كبير في السن (أكثر من 50)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
                <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>جنس المُهدى إليه</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر الجنس" /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="male">ذكر</SelectItem>
                        <SelectItem value="female">أنثى</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الميزانية (تقريبي)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="حدد ميزانيتك" /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="low">بسيطة (أقل من 100 ريال)</SelectItem>
                        <SelectItem value="medium">متوسطة (100 - 500 ريال)</SelectItem>
                        <SelectItem value="high">مرتفعة (500 - 1000 ريال)</SelectItem>
                        <SelectItem value="luxury">فاخرة (أكثر من 1000 ريال)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="occasionDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>وصف إضافي للمناسبة (اختياري)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="مثال: حفل تخرج من الجامعة، أو عيد زواجنا الخامس..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="personDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>وصف الشخص (اهتماماته، طباعه - اختياري)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="مثال: هو يحب القراءة والسفر، أو هي تحب الديكور والعناية بالبشرة..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Lightbulb className="ml-2 h-4 w-4" />}
              {isLoading ? 'جاري التفكير...' : 'اقترح لي هدايا'}
            </Button>
          </form>
        </Form>
        
        {(isLoading || suggestions.length > 0) && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-4 text-center">اقتراحاتنا لك</h3>
             {isLoading && (
              <div className="space-y-3">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-muted/50 rounded-lg border animate-pulse">
                    <div className="p-3 bg-primary/10 rounded-full"><Gift className="h-6 w-6 text-primary" /></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-full"></div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            {!isLoading && suggestions.length > 0 && (
              <div className="space-y-3">
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-secondary/50 rounded-lg border">
                    <div className="p-3 bg-primary/10 rounded-full">
                       <Gift className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold">{suggestion.name}</h4>
                      <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

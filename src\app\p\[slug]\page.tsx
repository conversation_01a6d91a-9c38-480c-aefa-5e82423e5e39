import { notFound } from 'next/navigation';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent } from '@/components/ui/card';
import { Metadata } from 'next';

const pages = {
  'privacy-policy': {
    title: 'سياسة الخصوصية',
    content: `
      <h2 class="text-xl font-bold mt-6 mb-4">مقدمة</h2>
      <p>نحن في "أدوات بالعربي" نحترم خصوصيتك ونلتزم بحمايتها. توضح سياسة الخصوصية هذه كيفية جمعنا واستخدامنا وحمايتنا لمعلوماتك الشخصية عند زيارتك لموقعنا الإلكتروني واستخدام خدماتنا.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">المعلومات التي نجمعها</h3>
      <p>قد نجمع الأنواع التالية من المعلومات:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li><strong>معلومات الاستخدام:</strong> بيانات حول كيفية استخدامك للموقع، مثل الصفحات التي تزورها والوقت المستغرق</li>
        <li><strong>معلومات الجهاز:</strong> نوع المتصفح، نظام التشغيل، عنوان IP</li>
        <li><strong>بيانات الأدوات:</strong> المعلومات التي تدخلها في أدواتنا المختلفة</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">كيفية استخدام المعلومات</h3>
      <p>نستخدم المعلومات المجمعة للأغراض التالية:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>تقديم وتحسين خدماتنا</li>
        <li>تحليل استخدام الموقع لتحسين الأداء</li>
        <li>عرض إعلانات مخصصة عبر Google AdSense</li>
        <li>الامتثال للمتطلبات القانونية</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">المعالجة من جانب العميل</h3>
      <p>معظم أدواتنا تقوم بمعالجة البيانات مباشرة في متصفحك (من جانب العميل). هذا يعني أن البيانات التي تدخلها لا تغادر جهازك أبدًا، مما يوفر أقصى درجات الخصوصية والأمان.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">ملفات تعريف الارتباط (Cookies)</h3>
      <p>نستخدم ملفات تعريف الارتباط وتقنيات مشابهة لـ:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>تحسين تجربة المستخدم</li>
        <li>تحليل حركة المرور على الموقع</li>
        <li>عرض إعلانات مخصصة</li>
        <li>حفظ تفضيلاتك</li>
      </ul>
      <p class="mt-2">يمكنك التحكم في ملفات تعريف الارتباط من خلال إعدادات متصفحك.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">Google AdSense والإعلانات</h3>
      <p>نستخدم Google AdSense لعرض الإعلانات على موقعنا. قد تستخدم Google ملفات تعريف الارتباط لعرض إعلانات مخصصة بناءً على زياراتك السابقة لموقعنا أو مواقع أخرى. يمكنك إلغاء الاشتراك في الإعلانات المخصصة من خلال زيارة إعدادات الإعلانات في Google.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">مشاركة المعلومات</h3>
      <p>لا نبيع أو نؤجر أو نشارك معلوماتك الشخصية مع أطراف ثالثة، باستثناء:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>عند الحصول على موافقتك الصريحة</li>
        <li>للامتثال للمتطلبات القانونية</li>
        <li>مع مقدمي الخدمات الموثوقين (مثل Google Analytics)</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">أمان البيانات</h3>
      <p>نتخذ تدابير أمنية مناسبة لحماية معلوماتك من الوصول غير المصرح به أو التغيير أو الكشف أو التدمير.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">حقوقك</h3>
      <p>لديك الحق في:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>الوصول إلى معلوماتك الشخصية</li>
        <li>تصحيح المعلومات غير الدقيقة</li>
        <li>طلب حذف معلوماتك</li>
        <li>الاعتراض على معالجة معلوماتك</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">التغييرات على سياسة الخصوصية</h3>
      <p>قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنقوم بإعلامك بأي تغييرات جوهرية عن طريق نشر السياسة الجديدة على هذه الصفحة مع تاريخ السريان المحدث.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">اتصل بنا</h3>
      <p>إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى التواصل معنا عبر صفحة <a href="/p/contact" class="text-primary hover:underline">اتصل بنا</a>.</p>
    `,
  },
  'terms-of-service': {
    title: 'شروط الخدمة',
    content: `
      <h2 class="text-xl font-bold mt-6 mb-4">مقدمة</h2>
      <p>مرحبًا بك في "أدوات بالعربي". باستخدامك لموقعنا الإلكتروني، فإنك توافق على الالتزام بشروط وأحكام الخدمة التالية. إذا كنت لا توافق على هذه الشروط، يرجى عدم استخدام موقعنا.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">قبول الشروط</h3>
      <p>باستخدام موقعنا، فإنك تؤكد أنك:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>تبلغ من العمر 18 عامًا على الأقل أو تستخدم الموقع تحت إشراف والديك</li>
        <li>تمتلك الأهلية القانونية للدخول في هذه الاتفاقية</li>
        <li>ستستخدم الموقع وفقًا لهذه الشروط</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">وصف الخدمة</h3>
      <p>يوفر موقع "أدوات بالعربي" مجموعة متنوعة من الأدوات والحاسبات والمحولات المجانية باللغة العربية، بما في ذلك:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>أدوات التحويل (التاريخ، العملات، الوحدات)</li>
        <li>الحاسبات المالية والرياضية</li>
        <li>أدوات النصوص والترجمة</li>
        <li>أدوات أخرى مفيدة للاستخدام اليومي</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">استخدام الأدوات</h3>
      <p>يتم توفير الأدوات على هذا الموقع "كما هي" ولأغراض إعلامية وتعليمية. نحن نبذل قصارى جهدنا لضمان دقة النتائج، ولكن:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>لا نضمن دقة النتائج بشكل مطلق</li>
        <li>يجب استخدام النتائج كدليل إرشادي فقط</li>
        <li>لا نتحمل أي مسؤولية عن القرارات المتخذة بناءً على النتائج</li>
        <li>ننصح بالتحقق من النتائج من مصادر موثوقة أخرى</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">الاستخدام المقبول</h3>
      <p>يُمنع استخدام الموقع لـ:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>أي أنشطة غير قانونية أو ضارة</li>
        <li>انتهاك حقوق الآخرين</li>
        <li>نشر محتوى مسيء أو غير لائق</li>
        <li>محاولة اختراق أو إلحاق الضرر بالموقع</li>
        <li>استخدام الموقع لأغراض تجارية دون إذن</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">الملكية الفكرية</h3>
      <p>جميع المحتويات على هذا الموقع، بما في ذلك النصوص والتصميم والشعارات والأكواد والأدوات، هي ملك لـ"أدوات بالعربي" ومحمية بموجب قوانين حقوق النشر والملكية الفكرية.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">الإعلانات</h3>
      <p>قد يحتوي موقعنا على إعلانات من أطراف ثالثة. نحن لسنا مسؤولين عن محتوى هذه الإعلانات أو المواقع التي تؤدي إليها.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">إخلاء المسؤولية</h3>
      <p>الأدوات والمعلومات المقدمة على هذا الموقع:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>ليست بديلاً عن المشورة المهنية المتخصصة</li>
        <li>لا تشكل نصيحة مالية أو قانونية أو طبية</li>
        <li>يجب التحقق منها قبل اتخاذ أي قرارات مهمة</li>
        <li>قد تحتوي على أخطاء أو عدم دقة</li>
      </ul>

      <h3 class="text-lg font-bold mt-6 mb-3">حدود المسؤولية</h3>
      <p>لا نتحمل أي مسؤولية عن أي أضرار مباشرة أو غير مباشرة قد تنتج عن استخدام موقعنا أو الاعتماد على المعلومات المقدمة فيه.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">التعديلات</h3>
      <p>نحتفظ بالحق في تعديل هذه الشروط في أي وقت. التعديلات ستصبح سارية فور نشرها على الموقع. استمرارك في استخدام الموقع بعد التعديلات يعني موافقتك على الشروط الجديدة.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">إنهاء الخدمة</h3>
      <p>نحتفظ بالحق في إنهاء أو تعليق وصولك إلى الموقع في أي وقت دون إشعار مسبق إذا انتهكت هذه الشروط.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">القانون المطبق</h3>
      <p>تخضع هذه الشروط للقوانين المعمول بها في المملكة العربية السعودية.</p>

      <h3 class="text-lg font-bold mt-6 mb-3">اتصل بنا</h3>
      <p>إذا كان لديك أي أسئلة حول شروط الخدمة هذه، يرجى التواصل معنا عبر صفحة <a href="/p/contact" class="text-primary hover:underline">اتصل بنا</a>.</p>
    `,
  },
};

type Props = {
  params: { slug: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const page = pages[slug as keyof typeof pages];
  if (!page) {
    return {};
  }
  return {
    title: page.title,
    description: `معلومات حول ${page.title} لموقع جامع الأدوات.`,
    robots: {
      index: true,
      follow: true,
    },
  };
}

import { ClientOnly } from '@/components/ClientOnly';

function PolicyContent({ page }: { page: any }) {
  const today = new Date().toLocaleDateString('ar-SA-u-nu-latn', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const finalContent = page.content.replace('{TODAY_DATE}', today);

  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader title={page.title} description={`آخر تحديث: ${today}`} />
      <Card>
        <CardContent className="p-6">
          <div
            className="prose prose-lg max-w-none space-y-4"
            dangerouslySetInnerHTML={{ __html: finalContent }}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default async function PolicyPage({ params }: Props) {
  const { slug } = await params;
  const page = pages[slug as keyof typeof pages];

  if (!page) {
    notFound();
  }

  return (
    <ClientOnly fallback={
      <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 md:p-8">
        <PageHeader title={page.title} description="جاري التحميل..." />
        <Card>
          <CardContent className="p-6">
            <div className="prose prose-lg max-w-none space-y-4">
              جاري تحميل المحتوى...
            </div>
          </CardContent>
        </Card>
      </div>
    }>
      <PolicyContent page={page} />
    </ClientOnly>
  );
}

export async function generateStaticParams() {
  return Object.keys(pages).map((slug) => ({
    slug,
  }));
}

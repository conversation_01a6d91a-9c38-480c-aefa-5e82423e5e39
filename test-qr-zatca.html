<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار QR Code - ZATCA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-section {
            border: 2px solid #333;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: #f9f9f9;
        }
        .data-section {
            background: #e8f4fd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        #qrResult {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار QR Code - ZATCA</h1>
        
        <div class="data-section">
            <h3>بيانات الاختبار:</h3>
            <p><strong>اسم البائع:</strong> <span id="sellerName">شركة الاختبار المحدودة</span></p>
            <p><strong>الرقم الضريبي:</strong> <span id="sellerVAT">123456789012345</span></p>
            <p><strong>تاريخ الفاتورة:</strong> <span id="invoiceDate">2024-01-15T14:30:00</span></p>
            <p><strong>المجموع الإجمالي:</strong> <span id="total">115.00</span> ريال</p>
            <p><strong>ضريبة القيمة المضافة:</strong> <span id="vatAmount">15.00</span> ريال</p>
        </div>

        <button class="test-button" onclick="generateTestQR()">إنشاء QR Code للاختبار</button>
        <button class="test-button" onclick="testQRReading()">اختبار قراءة QR Code</button>

        <div class="qr-section">
            <h3>QR Code المُنشأ:</h3>
            <div id="qrDisplay">سيظهر QR Code هنا بعد الضغط على زر الاختبار</div>
        </div>

        <div id="qrResult"></div>
    </div>

    <script>
        function toHex(str) {
            let hex = '';
            for (let i = 0; i < str.length; i++) {
                hex += str.charCodeAt(i).toString(16).padStart(2, '0');
            }
            return hex;
        }

        function createTLV(tag, value) {
            const tagHex = tag.toString(16).padStart(2, '0');
            const valueHex = toHex(value);
            const lengthHex = (valueHex.length / 2).toString(16).padStart(2, '0');
            return tagHex + lengthHex + valueHex;
        }

        function generateZATCAQRData() {
            const sellerName = document.getElementById('sellerName').textContent;
            const sellerVAT = document.getElementById('sellerVAT').textContent;
            const dateTime = document.getElementById('invoiceDate').textContent;
            const total = document.getElementById('total').textContent;
            const vatAmount = document.getElementById('vatAmount').textContent;

            const tlvData = 
                createTLV(1, sellerName) +
                createTLV(2, sellerVAT) +
                createTLV(3, dateTime) +
                createTLV(4, total) +
                createTLV(5, vatAmount);

            const bytes = [];
            for (let i = 0; i < tlvData.length; i += 2) {
                bytes.push(parseInt(tlvData.substring(i, i + 2), 16));
            }
            
            return btoa(String.fromCharCode.apply(null, bytes));
        }

        function generateTestQR() {
            const qrData = generateZATCAQRData();
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrData)}&size=200x200&format=png&margin=0`;
            
            document.getElementById('qrDisplay').innerHTML = `
                <img src="${qrUrl}" alt="ZATCA QR Code" style="max-width: 200px; border: 1px solid #ccc;">
                <br><br>
                <strong>البيانات المُشفرة (Base64):</strong><br>
                <textarea style="width: 100%; height: 100px; margin-top: 10px; font-family: monospace; font-size: 12px;">${qrData}</textarea>
            `;

            document.getElementById('qrResult').innerHTML = `
                <h3>تفاصيل QR Code:</h3>
                <p><strong>حالة الإنشاء:</strong> تم إنشاء QR Code بنجاح ✅</p>
                <p><strong>التنسيق:</strong> ZATCA TLV Base64</p>
                <p><strong>الحجم:</strong> 200x200 بكسل</p>
                <p><strong>يمكن قراءته:</strong> نعم، باستخدام أي قارئ QR Code</p>
                <p><strong>رابط الاختبار:</strong> <a href="${qrUrl}" target="_blank">فتح QR Code في نافذة جديدة</a></p>
            `;
        }

        function testQRReading() {
            alert('لاختبار قراءة QR Code:\n\n1. استخدم تطبيق قارئ QR Code على هاتفك\n2. وجه الكاميرا نحو الرمز المُنشأ\n3. يجب أن تظهر البيانات المُشفرة\n\nأو يمكنك استخدام أداة قارئ QR Code في الموقع');
        }
    </script>
</body>
</html>

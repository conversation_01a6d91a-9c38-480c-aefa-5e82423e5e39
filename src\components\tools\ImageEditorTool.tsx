

'use client';

import { useState, useRef, useEffect, ChangeEvent, MouseEvent } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Upload, Download, Type, SlidersHorizontal, Trash2, Crop, Palette } from 'lucide-react';
import { saveAs } from 'file-saver';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';

type Filter = 'none' | 'grayscale' | 'sepia' | 'invert' | 'contrast' | 'brightness' | 'blur';

interface TextElement {
  id: number;
  text: string;
  font: string;
  size: number;
  color: string;
  x: number;
  y: number;
}

const socialMediaSizes: Record<string, { width: number; height: number; label: string }> = {
  instagram_post: { width: 1080, height: 1080, label: 'منشور انستغرام (مربع)' },
  instagram_story: { width: 1080, height: 1920, label: 'قصة انستغرام (ستوري)' },
  twitter_post: { width: 1600, height: 900, label: 'منشور تويتر/X (صورة)' },
  facebook_post: { width: 1200, height: 630, label: 'منشور فيسبوك' },
};

const popularColors = [
    { name: 'White', hex: '#FFFFFF' }, { name: 'Yellow', hex: '#FFFF00' }, { name: 'Blue', hex: '#0000FF' }, { name: 'Red', hex: '#FF0000' }, { name: 'Green', hex: '#008000' }, { name: 'Brown', hex: '#A52A2A' }, { name: 'Black', hex: '#000000' },
    { name: 'Ivory', hex: '#FFFFF0' }, { name: 'Beige', hex: '#F5F5DC' }, { name: 'Wheat', hex: '#F5DEB3' }, { name: 'Khaki', hex: '#C3B091' }, { name: 'Golden', hex: '#FFD700' }, { name: 'Coral', hex: '#FF7F50' }, { name: 'Salmon', hex: '#FA8072' },
    { name: 'Hot Pink', hex: '#FF69B4' }, { name: 'Fuchsia', hex: '#FF00FF' }, { name: 'Lavender', hex: '#E6E6FA' }, { name: 'Plum', hex: '#DDA0DD' }, { name: 'Indigo', hex: '#4B0082' }, { name: 'Maroon', hex: '#800000' }, { name: 'Crimson', hex: '#DC143C' },
    { name: 'Silver', hex: '#C0C0C0' }, { name: 'Gray', hex: '#808080' }, { name: 'Charcoal', hex: '#36454F' }, { name: 'Pea', hex: '#9EB94E' }, { name: 'Olive', hex: '#808000' }, { name: 'Lime', hex: '#00FF00' }, { name: 'Teal', hex: '#008080' },
    { name: 'Navy Blue', hex: '#000080' }, { name: 'Royal Blue', hex: '#4169E1' }, { name: 'Azure', hex: '#007FFF' }, { name: 'Cyan', hex: '#00FFFF' }, { name: 'Aquamarine', hex: '#7FFFD4' }, { name: 'Orange', hex: '#FFA500' }, { name: 'Magenta', hex: '#FF00FF' },
];


export function ImageEditorTool() {
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [texts, setTexts] = useState<TextElement[]>([]);
  const [activeTextId, setActiveTextId] = useState<number | null>(null);
  const [filter, setFilter] = useState<string>('none');
  const [filterValue, setFilterValue] = useState(100);
  const [outputWidth, setOutputWidth] = useState(1080);
  const [outputHeight, setOutputHeight] = useState(1080);
  const [fillMode, setFillMode] = useState(false);
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const getTransformedCoords = (e: MouseEvent<HTMLCanvasElement>): { x: number; y: number } => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) * (canvas.width / rect.width);
    const y = (e.clientY - rect.top) * (canvas.height / rect.height);
    return { x, y };
  };
  
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !image) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = outputWidth;
    canvas.height = outputHeight;

    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.filter = filter === 'none' ? 'none' : 
                 filter === 'blur' ? `blur(${filterValue / 20}px)` : 
                 `${filter}(${filterValue}%)`;

    const canvasRatio = canvas.width / canvas.height;
    const imageRatio = image.width / image.height;
    
    let sx = 0, sy = 0, sWidth = image.width, sHeight = image.height;
    let dx = 0, dy = 0, dWidth = canvas.width, dHeight = canvas.height;

    if (fillMode) {
      if (imageRatio > canvasRatio) {
        sWidth = image.height * canvasRatio;
        sx = (image.width - sWidth) / 2;
      } else {
        sHeight = image.width / canvasRatio;
        sy = (image.height - sHeight) / 2;
      }
    } else {
      if (imageRatio > canvasRatio) {
        dHeight = canvas.width / imageRatio;
        dy = (canvas.height - dHeight) / 2;
      } else {
        dWidth = canvas.height * imageRatio;
        dx = (canvas.width - dWidth) / 2;
      }
    }
    
    ctx.drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
    
    ctx.filter = 'none';
    
    texts.forEach(({ id, text, font, size, color, x, y }) => {
      ctx.font = `${size}px ${font}`;
      ctx.fillStyle = color;
      ctx.textAlign = 'center';
      ctx.direction = 'rtl';
      ctx.textBaseline = 'middle';
      ctx.fillText(text, x, y);
      
      if (id === activeTextId) {
        ctx.strokeStyle = 'rgba(0, 123, 255, 0.7)';
        ctx.lineWidth = 2;
        const textWidth = ctx.measureText(text).width;
        ctx.strokeRect(x - textWidth / 2 - 5, y - size / 2 - 5, textWidth + 10, size + 10);
      }
    });
  };
  
  useEffect(() => {
    drawCanvas();
  }, [image, texts, filter, filterValue, outputWidth, outputHeight, fillMode, activeTextId]);

  const handleMouseDown = (e: MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getTransformedCoords(e);
    const ctx = canvasRef.current?.getContext('2d');
    if(!ctx) return;

    // Check if clicking on an existing text to activate/drag
    for (let i = texts.length - 1; i >= 0; i--) {
        const text = texts[i];
        ctx.font = `${text.size}px ${text.font}`;
        const textWidth = ctx.measureText(text.text).width;
        if (
            x >= text.x - textWidth / 2 && x <= text.x + textWidth / 2 &&
            y >= text.y - text.size / 2 && y <= text.y + text.size / 2
        ) {
            setActiveTextId(text.id);
            setIsDragging(true);
            setDragStart({ x: x - text.x, y: y - text.y });
            return;
        }
    }
    
    // If not clicking on any text, deactivate
    setActiveTextId(null);
  };

  const handleMouseMove = (e: MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !activeTextId) return;
    const { x, y } = getTransformedCoords(e);
    updateText(activeTextId, { x: x - dragStart.x, y: y - dragStart.y });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new window.Image();
        img.onload = () => {
          setImage(img);
          setTexts([]);
          setOutputWidth(img.width);
          setOutputHeight(img.height);
          toast({ title: "تم تحميل الصورة بنجاح!" });
        };
        img.src = event.target?.result as string;
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };

  const addText = () => {
    if (!image) return;
    const newText: TextElement = {
      id: Date.now(),
      text: 'نص تجريبي',
      font: 'Arial',
      size: 40,
      color: '#000000',
      x: outputWidth / 2,
      y: outputHeight / 2,
    };
    setTexts([...texts, newText]);
    setActiveTextId(newText.id);
  };
  
  const updateText = (id: number, updates: Partial<TextElement>) => {
    setTexts(texts.map(t => t.id === id ? { ...t, ...updates } : t));
  };
  
  const handleDownload = (format: 'png' | 'jpeg') => {
    setActiveTextId(null);
    setTimeout(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        canvas.toBlob((blob) => {
          if (blob) saveAs(blob, `edited-image.${format}`);
        }, `image/${format}`);
    }, 100);
  };

  const handlePresetChange = (presetKey: string) => {
    const size = socialMediaSizes[presetKey];
    if (size) {
      setOutputWidth(size.width);
      setOutputHeight(size.height);
    }
  };

  const activeText = texts.find(t => t.id === activeTextId);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2">
        <Card className="overflow-hidden">
          <CardContent className="p-2 bg-muted flex items-center justify-center">
            {image ? (
              <canvas 
                ref={canvasRef} 
                className="max-w-full max-h-[70vh] object-contain cursor-grab active:cursor-grabbing"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
              />
            ) : (
              <div 
                className="w-full h-96 border-2 border-dashed rounded-lg flex flex-col items-center justify-center text-muted-foreground hover:border-primary cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-12 w-12 mb-4" />
                <p>انقر هنا أو اسحب صورة لبدء التحرير</p>
                <input ref={fileInputRef} type="file" accept="image/*" className="hidden" onChange={handleImageUpload} />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>أدوات التحرير</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="size">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="size"><Crop className="ml-2 h-4 w-4"/>الحجم</TabsTrigger>
                <TabsTrigger value="text"><Type className="ml-2 h-4 w-4"/>نص</TabsTrigger>
                <TabsTrigger value="filters"><SlidersHorizontal className="ml-2 h-4 w-4"/>فلاتر</TabsTrigger>
              </TabsList>
              
              <TabsContent value="size" className="pt-4 space-y-4">
                  <Select onValueChange={handlePresetChange}>
                    <SelectTrigger><SelectValue placeholder="اختر حجمًا جاهزًا" /></SelectTrigger>
                    <SelectContent>
                      {Object.entries(socialMediaSizes).map(([key, { label }]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="grid grid-cols-2 gap-2">
                    <Input type="number" placeholder="العرض" value={outputWidth} onChange={e => setOutputWidth(Number(e.target.value))} />
                    <Input type="number" placeholder="الارتفاع" value={outputHeight} onChange={e => setOutputHeight(Number(e.target.value))} />
                  </div>
                   <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch id="fill-mode" checked={fillMode} onCheckedChange={setFillMode} />
                    <Label htmlFor="fill-mode">ملء المساحة (قص الصورة)</Label>
                  </div>
              </TabsContent>

              <TabsContent value="text" className="pt-4 space-y-4">
                <Button onClick={addText} disabled={!image} className="w-full">إضافة نص جديد</Button>
                <div className="max-h-32 overflow-y-auto space-y-2">
                  {texts.map((t, i) => (
                      <Button key={t.id} variant={activeTextId === t.id ? 'default' : 'outline'} onClick={() => setActiveTextId(t.id)} className="w-full justify-between">
                        <span className="truncate">نص {i+1}: {t.text}</span>
                        <Trash2 className="h-4 w-4 text-destructive/80 hover:text-destructive" onClick={(e) => { e.stopPropagation(); setTexts(texts.filter(txt => txt.id !== t.id)); if (activeTextId === t.id) setActiveTextId(null); }} />
                      </Button>
                  ))}
                </div>
                {activeText && (
                  <div className="p-3 border rounded-lg space-y-4">
                     <Textarea 
                       value={activeText.text}
                       onChange={(e) => updateText(activeTextId!, { text: e.target.value })}
                       className="text-right"
                       placeholder="اكتب هنا"
                     />
                     <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                           <Input type="color" value={activeText.color} onChange={(e) => updateText(activeTextId!, { color: e.target.value })} className="p-1 h-10 w-full"/>
                           <Input type="number" value={activeText.size} onChange={(e) => updateText(activeTextId!, { size: Number(e.target.value) })} placeholder="الحجم" />
                        </div>
                         <div className="grid grid-cols-7 gap-2 pt-2">
                            {popularColors.map(color => (
                                <button 
                                    key={color.hex} 
                                    title={color.name}
                                    onClick={() => updateText(activeTextId!, { color: color.hex })}
                                    className={cn('w-6 h-6 rounded-md border-2', activeText.color.toUpperCase() === color.hex.toUpperCase() ? 'border-primary' : 'border-gray-200')}
                                    style={{ backgroundColor: color.hex }}
                                />
                            ))}
                        </div>
                     </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="filters" className="pt-4 space-y-4">
                <Select onValueChange={(val) => setFilter(val)} defaultValue={filter}>
                  <SelectTrigger><SelectValue/></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">بدون فلتر</SelectItem>
                    <SelectItem value="grayscale">رمادي</SelectItem>
                    <SelectItem value="sepia">بني داكن</SelectItem>
                    <SelectItem value="invert">عكس الألوان</SelectItem>
                    <SelectItem value="contrast">تباين</SelectItem>
                    <SelectItem value="brightness">سطوع</SelectItem>
                    <SelectItem value="blur">ضبابي</SelectItem>
                  </SelectContent>
                </Select>
                {filter !== 'none' && (
                  <Slider defaultValue={[filterValue]} max={filter === 'blur' ? 100 : 200} step={1} onValueChange={(val) => setFilterValue(val[0])} />
                )}
              </TabsContent>

            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>تصدير</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-2">
            <Button onClick={() => handleDownload('png')} disabled={!image}><Download className="ml-2 h-4 w-4"/>PNG</Button>
            <Button onClick={() => handleDownload('jpeg')} disabled={!image}><Download className="ml-2 h-4 w-4"/>JPG</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

    

'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Image as ImageIcon, Loader2, Archive } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import JSZip from 'jszip';

// Declare global types for PDF.js
declare global {
  interface Window {
    pdfjsLib: any;
  }
}

interface ConvertedImage {
  pageNumber: number;
  dataUrl: string;
  fileName: string;
  originalFile: string;
}

export function PdfToJpgTool() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [convertedImages, setConvertedImages] = useState<ConvertedImage[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressText, setProgressText] = useState('');
  const [pdfLibLoaded, setPdfLibLoaded] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Load PDF.js library
  useEffect(() => {
    const loadPdfLib = () => {
      if (typeof window !== 'undefined' && !window.pdfjsLib) {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
        script.onload = () => {
          if (window.pdfjsLib) {
            window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
            setPdfLibLoaded(true);
          }
        };
        script.onerror = () => {
          setError('فشل في تحميل مكتبة PDF.js. يرجى إعادة تحميل الصفحة.');
        };
        document.head.appendChild(script);
      } else if (window.pdfjsLib) {
        setPdfLibLoaded(true);
      }
    };

    loadPdfLib();
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');

    if (pdfFiles.length === 0) {
      setError('يرجى اختيار ملفات PDF صالحة.');
      return;
    }

    // Check file sizes (max 10MB per file)
    const oversizedFiles = pdfFiles.filter(file => file.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      setError('بعض الملفات كبيرة جداً. يرجى اختيار ملفات أصغر من 10 ميجابايت.');
      return;
    }

    setSelectedFiles(pdfFiles);
    setError(null);
    setSuccess(null);
    setConvertedImages([]);

    toast({
      title: 'تم اختيار الملفات',
      description: `تم اختيار ${pdfFiles.length} ملف PDF للتحويل.`,
    });
  };

  const convertPDFs = async () => {
    if (selectedFiles.length === 0 || !pdfLibLoaded) return;

    setIsConverting(true);
    setError(null);
    setSuccess(null);
    setConvertedImages([]);
    setProgress(0);

    try {
      const allImages: ConvertedImage[] = [];

      for (let fileIndex = 0; fileIndex < selectedFiles.length; fileIndex++) {
        const file = selectedFiles[fileIndex];
        setProgressText(`معالجة ${file.name}...`);

        const arrayBuffer = await file.arrayBuffer();
        const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;

        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const scale = 2; // Higher scale for better quality
          const viewport = page.getViewport({ scale: scale });

          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');

          if (!context) {
            throw new Error('فشل في إنشاء canvas context');
          }

          canvas.height = viewport.height;
          canvas.width = viewport.width;

          await page.render({
            canvasContext: context,
            viewport: viewport
          }).promise;

          const imageDataUrl = canvas.toDataURL('image/jpeg', 0.95);
          const fileName = `${file.name.replace('.pdf', '')}_page_${pageNum}.jpg`;

          allImages.push({
            pageNumber: pageNum,
            dataUrl: imageDataUrl,
            fileName: fileName,
            originalFile: file.name
          });

          // Update progress
          const totalProgress = ((fileIndex * 100) + (pageNum * 100 / pdf.numPages)) / selectedFiles.length;
          setProgress(Math.min(totalProgress, 100));
        }
      }

      setConvertedImages(allImages);
      setProgress(100);
      setProgressText('تم التحويل بنجاح!');
      setSuccess(`تم تحويل ${allImages.length} صفحة إلى صور JPG بنجاح!`);

    } catch (err) {
      console.error('PDF conversion error:', err);
      setError('فشل في تحويل ملف PDF. يرجى التأكد من أن الملف صالح وغير محمي بكلمة مرور.');
    } finally {
      setIsConverting(false);
      setTimeout(() => {
        setProgress(0);
        setProgressText('');
      }, 3000);
    }
  };

  const downloadImage = (image: ConvertedImage) => {
    // Convert data URL to blob for better browser compatibility
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = function() {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);

      canvas.toBlob(function(blob) {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = image.fileName;
          link.href = url;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the object URL
          setTimeout(() => URL.revokeObjectURL(url), 1000);
        }
      }, 'image/jpeg', 0.95);
    };

    img.src = image.dataUrl;

    toast({
      title: 'تم التحميل',
      description: `تم تحميل ${image.fileName}`,
    });
  };

  const downloadAll = async () => {
    if (convertedImages.length === 0) return;

    try {
      const zip = new JSZip();

      // Show loading toast
      toast({
        title: 'إنشاء ملف ZIP',
        description: `جاري ضغط ${convertedImages.length} صورة...`,
      });

      // Add each image to the ZIP file
      for (const image of convertedImages) {
        // Convert data URL to blob
        const response = await fetch(image.dataUrl);
        const blob = await response.blob();

        // Add to ZIP with the correct filename
        zip.file(image.fileName, blob);
      }

      // Generate ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // Create download link
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;

      // Create ZIP filename based on original files
      const originalFiles = [...new Set(convertedImages.map(img => img.originalFile))];
      const zipFileName = originalFiles.length === 1
        ? `${originalFiles[0].replace('.pdf', '')}_images.zip`
        : `converted_pdf_images_${new Date().getTime()}.zip`;

      link.download = zipFileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the object URL
      setTimeout(() => URL.revokeObjectURL(url), 1000);

      toast({
        title: 'تم إنشاء ملف ZIP',
        description: `تم تحميل ${convertedImages.length} صورة في ملف ${zipFileName}`,
      });
    } catch (error) {
      console.error('Error creating ZIP file:', error);
      toast({
        title: 'خطأ في إنشاء ملف ZIP',
        description: 'حدث خطأ أثناء ضغط الصور. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      });
    }
  };

  const clearAll = () => {
    setSelectedFiles([]);
    setConvertedImages([]);
    setError(null);
    setSuccess(null);
    setProgress(0);
    setProgressText('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          تحويل PDF إلى JPG
        </CardTitle>
        <CardDescription className="text-base leading-relaxed">
          <div className="space-y-3">
            <p>
              <strong>أداة تحويل PDF إلى JPG المجانية والمتقدمة</strong> - حول ملفات PDF إلى صور JPG عالية الجودة بسهولة تامة وبدون الحاجة لتثبيت أي برامج.
              أداتنا تدعم تحويل جميع صفحات PDF إلى صور منفصلة بجودة احترافية تصل إلى دقة 2x من الحجم الأصلي.
            </p>
            <p>
              <strong>مميزات فريدة:</strong> تحميل الصور بشكل فردي أو جميعها في ملف ZIP واحد منظم، معالجة محلية 100% لضمان خصوصية ملفاتك،
              دعم ملفات PDF متعددة الصفحات حتى 10 ميجابايت، واجهة عربية سهلة الاستخدام، وسرعة تحويل فائقة.
            </p>
            <p>
              <strong>الاستخدامات:</strong> مثالية لاستخراج صفحات معينة من المستندات، تحويل الكتب الإلكترونية لصور، مشاركة محتوى PDF على وسائل التواصل،
              إنشاء أرشيف صور للمستندات المهمة، والتحضير للطباعة أو العروض التقديمية.
            </p>
          </div>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Loading Status */}
        {!pdfLibLoaded && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
              <span className="text-blue-900">جاري تحميل مكتبة PDF.js...</span>
            </div>
          </div>
        )}

        {/* File Upload Section */}
        <div className="space-y-4">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                {selectedFiles.length > 0
                  ? `تم اختيار ${selectedFiles.length} ملف PDF`
                  : 'اختر ملفات PDF لتحويلها إلى صور JPG'
                }
              </p>
              <Button
                disabled={!pdfLibLoaded}
                variant="outline"
              >
                <Upload className="ml-2 h-4 w-4" />
                اختر ملفات PDF
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept=".pdf,application/pdf"
                multiple
                onChange={handleFileChange}
              />
            </div>
          </div>

          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              {selectedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-blue-900">{file.name}</p>
                      <p className="text-sm text-blue-600">
                        {(file.size / 1024 / 1024).toFixed(2)} ميجابايت
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              <div className="flex gap-2 pt-2">
                <Button
                  onClick={convertPDFs}
                  disabled={isConverting || !pdfLibLoaded}
                  className="flex-1"
                >
                  {isConverting ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري التحويل...
                    </>
                  ) : (
                    <>
                      <ImageIcon className="ml-2 h-4 w-4" />
                      تحويل إلى JPG
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  <Trash2 className="ml-2 h-4 w-4" />
                  مسح الكل
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Progress Section */}
        {isConverting && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{progressText}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success Display */}
        {success && (
          <Alert className="border-green-200 bg-green-50">
            <FileText className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* Results Section */}
        {convertedImages.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                الصور المحولة ({convertedImages.length} صفحة)
              </h3>
              <Button onClick={downloadAll} variant="outline">
                <Archive className="ml-2 h-4 w-4" />
                تحميل كملف ZIP
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {convertedImages.map((image) => (
                <div key={`${image.originalFile}-${image.pageNumber}`} className="border rounded-lg p-4 space-y-3">
                  <div className="aspect-[3/4] bg-gray-100 rounded overflow-hidden">
                    <img
                      src={image.dataUrl}
                      alt={`صفحة ${image.pageNumber}`}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">
                      صفحة {image.pageNumber}
                    </p>
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => downloadImage(image)}
                    >
                      <Download className="ml-2 h-3 w-3" />
                      تحميل
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">تعليمات الاستخدام:</h4>
          <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
            <li>اختر ملفات PDF من جهازك (حد أقصى 10 ميجابايت لكل ملف)</li>
            <li>انقر على "تحويل إلى JPG" لبدء عملية التحويل</li>
            <li>ستظهر جميع صفحات PDF كصور JPG منفصلة</li>
            <li>يمكنك تحميل كل صورة على حدة أو تحميل جميع الصور في ملف ZIP واحد</li>
            <li>ملف ZIP يحتوي على جميع الصور مع أسماء واضحة ومنظمة</li>
            <li>جودة الصور عالية ومناسبة للطباعة والمشاركة</li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-5 w-5 text-green-600" />
            <h4 className="font-medium text-green-900">الخصوصية والأمان</h4>
          </div>
          <p className="text-sm text-green-700">
            جميع عمليات التحويل تتم محلياً في متصفحك. لا يتم رفع ملفاتك إلى أي خادم، مما يضمن خصوصية وأمان مستنداتك بالكامل.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

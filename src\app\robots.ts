import { MetadataRoute } from 'next';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://adawat.org';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      // Allow all web crawlers to access all content
      {
        userAgent: '*',
        allow: '/',
        crawlDelay: 1,
        disallow: [
          '/admin/',
          '/private/',
          '/_next/static/',
          '/_next/image/',
          '/api/auth/',
          '/api/admin/',
          '/.well-known/security.txt',
          '/node_modules/',
          '/.git/',
          '/.env',
          '/package.json',
          '/package-lock.json',
          '/test*',
          '/*?*',
        ],
      },
      // Enhanced rules for major search engines with Arabic content optimization
      {
        userAgent: 'Googlebot',
        allow: '/',
        crawlDelay: 0.5,
      },
      {
        userAgent: 'Googlebot-Image',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot-News',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot-Video',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'MSNBot',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Slurp',
        allow: '/',
        crawlDelay: 2,
      },
      {
        userAgent: 'DuckDuckBot',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Baiduspider',
        allow: '/',
        crawlDelay: 2,
      },
      {
        userAgent: 'YandexBot',
        allow: '/',
        crawlDelay: 1,
      },
      // Social media bots for better sharing
      {
        userAgent: 'facebookexternalhit',
        allow: '/',
      },
      {
        userAgent: 'Twitterbot',
        allow: '/',
      },
      {
        userAgent: 'LinkedInBot',
        allow: '/',
      },
      {
        userAgent: 'WhatsApp',
        allow: '/',
      },
      {
        userAgent: 'Pinterestbot',
        allow: '/',
      },
      // Block common bot traps and unwanted crawlers
      {
        userAgent: 'AhrefsBot',
        disallow: '/',
      },
      {
        userAgent: 'MJ12bot',
        disallow: '/',
      },
      {
        userAgent: 'DotBot',
        disallow: '/',
      },
      {
        userAgent: 'SemrushBot',
        disallow: '/',
      },
      {
        userAgent: 'MajesticSEO',
        disallow: '/',
      },
      {
        userAgent: 'Rogerbot',
        disallow: '/',
      },
      {
        userAgent: 'Exabot',
        disallow: '/',
      },
    ],
    sitemap: `${siteUrl}/sitemap.xml`,
    host: siteUrl,
  };
}


import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, Home, SearchX } from 'lucide-react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'الصفحة غير موجودة (404)',
  description: 'لم نتمكن من العثور على الصفحة التي تبحث عنها.',
};

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-8rem)] text-center p-6">
      <SearchX className="w-24 h-24 text-primary/50 mb-6" />
      <h1 className="text-6xl font-bold font-headline text-primary">404</h1>
      <h2 className="text-2xl font-semibold mt-4 mb-2">الصفحة غير موجودة</h2>
      <p className="text-muted-foreground max-w-md mx-auto mb-8">
        عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها. ربما تم حذفها أو تغيير اسمها أو أنها لم تكن موجودة من الأصل.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/">
          <Button size="lg">
            <Home className="ml-2 h-4 w-4" />
            العودة إلى الصفحة الرئيسية
          </Button>
        </Link>
        <Link href="/p/contact">
          <Button size="lg" variant="outline">
            اتصل بنا
            <ArrowRight className="mr-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
    </div>
  );
}

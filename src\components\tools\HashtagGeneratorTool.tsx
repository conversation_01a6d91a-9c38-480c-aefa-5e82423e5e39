
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Co<PERSON>, Hash, Loader2, Sparkles, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { generateHashtags, HashtagOutput } from '@/ai/flows/hashtag-generator-flow';
import { Badge } from '../ui/badge';

const FormSchema = z.object({
  keyword: z.string().min(2, {
    message: 'الرجاء إدخال كلمة مفتاحية من حرفين على الأقل.',
  }),
});

type FormValues = z.infer<typeof FormSchema>;

const HashtagCategory = ({ title, hashtags, onCopy }: { title: string, hashtags: string[], onCopy: (text: string) => void }) => {
  const fullHashtagString = hashtags.map(h => `#${h}`).join(' ');
  return (
    <Card className="bg-muted/50">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">{title}</CardTitle>
          <Button variant="ghost" size="sm" onClick={() => onCopy(fullHashtagString)}>
            <Copy className="ml-2 h-4 w-4" />
            نسخ الكل
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {hashtags.map((tag, index) => (
            <Badge 
              key={index} 
              variant="secondary"
              className="cursor-pointer"
              onClick={() => onCopy(`#${tag}`)}
            >
              #{tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export function HashtagGeneratorTool() {
  const [hashtags, setHashtags] = useState<HashtagOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    setHashtags(null);
    try {
      const result = await generateHashtags(data);
      setHashtags(result);
    } catch (error) {
      console.error("AI hashtag generation error:", error);
      toast({
        variant: 'destructive',
        title: 'حدث خطأ',
        description: 'لم نتمكن من توليد الهاشتاغات. يرجى المحاولة مرة أخرى.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "تم النسخ!",
        description: `"${text}" تم نسخه إلى الحافظة.`,
      });
    });
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>مولد الهاشتاغات العربية بالذكاء الاصطناعي</CardTitle>
        <CardDescription>
          أدخل كلمة مفتاحية واحصل على أفضل الهاشتاغات لزيادة وصولك وتفاعلك على وسائل التواصل.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="keyword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الكلمة المفتاحية أو الموضوع</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Hash className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input placeholder="مثال: تسويق رقمي، طبخ، سفر" className="pr-10" {...field} />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Wand2 className="ml-2 h-4 w-4" />}
              {isLoading ? 'جاري التفكير...' : 'ولّد الهاشتاغات'}
            </Button>
          </form>
        </Form>
        
        {isLoading && (
          <div className="mt-8 text-center text-muted-foreground space-y-2">
             <Loader2 className="mx-auto h-8 w-8 animate-spin" />
             <p>يقوم خبيرنا الرقمي بتجهيز أفضل الهاشتاغات لك...</p>
          </div>
        )}

        {hashtags && (
          <div className="mt-8 space-y-4">
            <HashtagCategory title="هاشتاغات شائعة" hashtags={hashtags.popular} onCopy={copyToClipboard} />
            <HashtagCategory title="هاشتاغات متخصصة" hashtags={hashtags.niche} onCopy={copyToClipboard} />
            <HashtagCategory title="هاشتاغات ذات صلة" hashtags={hashtags.related} onCopy={copyToClipboard} />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

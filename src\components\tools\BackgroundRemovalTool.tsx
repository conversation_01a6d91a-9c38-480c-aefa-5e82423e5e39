'use client';

import { useState, useRef } from 'react';
import { removeBackground } from '@imgly/background-removal';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, Wand2, Loader2, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import Image from 'next/image';

type Stage = 'initial' | 'processing' | 'result';

export function BackgroundRemovalTool() {
  const [stage, setStage] = useState<Stage>('initial');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة صالح.');
      setStage('initial');
      return;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      setError('حجم الملف كبير جدًا. يرجى اختيار ملف أصغر من 10 ميجابايت.');
      setStage('initial');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setOriginalImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    
    setSelectedFile(file);
    setError(null);
    setProcessedImage(null);
    setStage('initial'); // Stay in initial stage to show preview and "remove" button
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const processImage = async () => {
    if (!selectedFile) return;

    setStage('processing');
    setError(null);
    setProcessedImage(null);
    
    try {
      const blob = await removeBackground(selectedFile);
      const url = URL.createObjectURL(blob);
      setProcessedImage(url);
      setStage('result');
      toast({ title: 'تمت إزالة الخلفية بنجاح!' });
    } catch (err: any) {
        console.error("Background removal error:", err);
        setError(`حدث خطأ أثناء المعالجة: ${err.message}. قد تكون الصورة معقدة جدًا أو أن متصفحك لا يدعم التقنيات المطلوبة.`);
        setStage('initial');
    }
  };

  const resetTool = () => {
    if (processedImage) {
        URL.revokeObjectURL(processedImage);
    }
    setSelectedFile(null);
    setError(null);
    setOriginalImage(null);
    setProcessedImage(null);
    setStage('initial');
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>أداة إزالة خلفية الصور مجانًا</CardTitle>
        <CardDescription>أزل خلفية أي صورة مجانًا. تعمل بالكامل في متصفحك لضمان خصوصيتك.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {error && <Alert variant="destructive" className="mb-4"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {stage === 'initial' && !selectedFile && (
           <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
              <div className="flex flex-col items-center gap-4">
                <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">انقر هنا لاختيار صورة</h3>
                  <p className="text-gray-600 mb-4">أو اسحبها وأفلتها في أي مكان</p>
                  <Button>اختر صورة</Button>
                </div>
              </div>
              <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*" className="hidden" />
            </div>
        )}

        {stage === 'initial' && selectedFile && (
          <div className="text-center space-y-4">
              <h3 className="font-semibold">الصورة المحددة</h3>
              <div className="max-w-md mx-auto">
                 <Image src={originalImage!} alt="Original" width={400} height={400} className="max-w-full mx-auto rounded-md border" />
              </div>
              <p className="text-sm text-muted-foreground">{selectedFile.name}</p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                 <Button onClick={processImage} size="lg">
                   <Wand2 className="ml-2 h-4 w-4" />
                   إزالة الخلفية
                 </Button>
                 <Button onClick={resetTool} variant="outline">
                   <Trash2 className="ml-2 h-4 w-4" /> تغيير الصورة
                 </Button>
              </div>
          </div>
        )}
        
        {stage === 'processing' && (
           <div className="text-center space-y-4 p-8 bg-muted rounded-lg">
                <Loader2 className="mx-auto h-12 w-12 text-primary animate-spin" />
                <p className="text-lg font-semibold">جاري المعالجة...</p>
                <p className="text-sm text-muted-foreground">قد تستغرق هذه العملية بضع ثوانٍ. تتم المعالجة بالكامل على جهازك.</p>
           </div>
        )}

        {stage === 'result' && processedImage && (
          <div className="text-center space-y-4">
            <h3 className="font-semibold">النتيجة (بخلفية شفافة)</h3>
             <div className="flex justify-center items-center gap-2 p-4 bg-gray-200 bg-cover rounded-md" style={{backgroundImage: 'url("data:image/svg+xml,%3csvg width=\'100%25\' height=\'100%25\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3crect width=\'100%25\' height=\'100%25\' fill=\'none\' stroke=\'%23DDDDDD\' stroke-width=\'4\' stroke-dasharray=\'6%2c 14\' stroke-linecap=\'square\'/%3e%3c/svg%3e")'}}>
                 <Image src={processedImage} alt="Processed" width={400} height={400} className="max-w-full mx-auto rounded-md" />
            </div>
             <div className="flex flex-col sm:flex-row gap-2 justify-center">
                 <Button onClick={() => saveAs(processedImage, `removed-bg-${selectedFile?.name}.png`)} className="flex-1">
                   <Download className="ml-2 h-4 w-4" />
                   تحميل الصورة (PNG)
                 </Button>
                 <Button onClick={resetTool} variant="outline" className="flex-1">
                   <ImageIcon className="ml-2 h-4 w-4" /> معالجة صورة أخرى
                 </Button>
             </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
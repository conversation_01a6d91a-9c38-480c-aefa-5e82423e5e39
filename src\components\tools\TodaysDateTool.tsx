'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { <PERSON>, Moon } from 'lucide-react';
import { ClientOnly, useSafeDate } from '@/components/ClientOnly';

function DateDisplay() {
  const { formatDate, formatHijriDate, mounted } = useSafeDate();

  if (!mounted) {
    return (
      <>
        <div className="p-4 sm:p-6 bg-blue-500/10 rounded-lg border border-blue-500/20 text-center">
          <div className="flex items-center justify-center gap-2 sm:gap-3 mb-3">
            <Calendar className="h-5 w-5 sm:h-7 sm:w-7 text-blue-600"/>
            <h3 className="text-lg sm:text-xl font-semibold text-blue-800">التاريخ الميلادي</h3>
          </div>
          <p className="text-lg sm:text-2xl md:text-3xl font-bold text-blue-700 font-mono leading-relaxed" dir="rtl">
            جاري التحميل...
          </p>
        </div>
        <div className="p-4 sm:p-6 bg-green-500/10 rounded-lg border border-green-500/20 text-center">
          <div className="flex items-center justify-center gap-2 sm:gap-3 mb-3">
            <Moon className="h-5 w-5 sm:h-7 sm:w-7 text-green-600"/>
            <h3 className="text-lg sm:text-xl font-semibold text-green-800">التاريخ الهجري</h3>
          </div>
          <p className="text-lg sm:text-2xl md:text-3xl font-bold text-green-700 font-mono leading-relaxed" dir="rtl">
            جاري التحميل...
          </p>
        </div>
      </>
    );
  }

  const today = new Date();
  const gregorianDate = formatDate(today, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const hijriDate = formatHijriDate(today, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <>
      <div className="p-4 sm:p-6 bg-blue-500/10 rounded-lg border border-blue-500/20 text-center">
        <div className="flex items-center justify-center gap-2 sm:gap-3 mb-3">
          <Calendar className="h-5 w-5 sm:h-7 sm:w-7 text-blue-600"/>
          <h3 className="text-lg sm:text-xl font-semibold text-blue-800">التاريخ الميلادي</h3>
        </div>
        <p className="text-lg sm:text-2xl md:text-3xl font-bold text-blue-700 font-mono leading-relaxed break-words" dir="rtl">
          {gregorianDate}
        </p>
      </div>
      <div className="p-4 sm:p-6 bg-green-500/10 rounded-lg border border-green-500/20 text-center">
        <div className="flex items-center justify-center gap-2 sm:gap-3 mb-3">
          <Moon className="h-5 w-5 sm:h-7 sm:w-7 text-green-600"/>
          <h3 className="text-lg sm:text-xl font-semibold text-green-800">التاريخ الهجري</h3>
        </div>
        <p className="text-lg sm:text-2xl md:text-3xl font-bold text-green-700 font-mono leading-relaxed break-words" dir="rtl">
          {hijriDate}
        </p>
      </div>
    </>
  );
}

export function TodaysDateTool() {

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl sm:text-2xl text-center">تاريخ اليوم</CardTitle>
        <CardDescription className="text-center text-sm sm:text-base">
          عرض التاريخ الحالي بالتقويمين الميلادي والهجري
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6 px-4 sm:px-6">
        <DateDisplay />
      </CardContent>
    </Card>
  );
}

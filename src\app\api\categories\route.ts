import { NextRequest, NextResponse } from 'next/server';
import { getCategories, createCategory } from '@/lib/articles-supabase';
import { ArticleCategory } from '@/types/article';

export async function GET() {
  try {
    const categories = await getCategories();
    return NextResponse.json({ categories });
  } catch (error) {
    console.error('Error in GET /api/categories:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الفئات' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'slug', 'description', 'color'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `الحقل ${field} مطلوب` },
          { status: 400 }
        );
      }
    }

    const categoryData: Omit<ArticleCategory, 'id' | 'createdAt' | 'updatedAt'> = {
      name: body.name,
      slug: body.slug,
      description: body.description,
      color: body.color,
      icon: body.icon,
      order: body.order || 0,
    };

    const categoryId = await createCategory(categoryData);
    
    return NextResponse.json(
      { id: categoryId, message: 'تم إنشاء الفئة بنجاح' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/categories:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفئة' },
      { status: 500 }
    );
  }
}

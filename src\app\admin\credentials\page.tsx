'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PageHeader } from '@/components/PageHeader';
import {
  Key,
  Copy,
  Eye,
  EyeOff,
  Shield,
  Database,
  CheckCircle,
  User
} from 'lucide-react';
import { AdminUser, getAllUsers, isAdmin, changeUserPassword, deleteUser, createUser } from '@/lib/auth-supabase';
import { useAuth } from '@/components/AuthProvider';
import AddUserForm from '@/components/AddUserForm';

export default function CredentialsPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPasswords, setShowPasswords] = useState(true);
  const [copiedUser, setCopiedUser] = useState<string | null>(null);
  const [editingPassword, setEditingPassword] = useState<string | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [showNewUserForm, setShowNewUserForm] = useState(false);
  const [newUserPasswords, setNewUserPasswords] = useState<{ [userId: string]: string }>({});
  const { user: currentUser } = useAuth();

  const userIsAdmin = isAdmin(currentUser);

  useEffect(() => {
    // تحميل كلمات المرور المحفوظة من localStorage
    const savedPasswords = localStorage.getItem('userPasswords');
    if (savedPasswords) {
      try {
        setNewUserPasswords(JSON.parse(savedPasswords));
      } catch (error) {
        console.error('Error loading saved passwords:', error);
      }
    }

    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      const usersData = await getAllUsers();
      setUsers(usersData);

      // تحميل كلمات المرور الافتراضية فقط للمستخدمين الجدد الذين لا يملكون كلمة مرور محفوظة
      const defaultPasswords: { [key: string]: string } = {
        'admin': 'admin123',
        'manager': 'manager123',
        'editor': 'editor123'
      };

      setNewUserPasswords(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        usersData.forEach(user => {
          // إضافة كلمة المرور الافتراضية فقط إذا لم تكن موجودة مسبقاً
          if (defaultPasswords[user.username] && !updated[user.id]) {
            updated[user.id] = defaultPasswords[user.username];
            hasChanges = true;
          }
        });

        // حفظ التحديثات في localStorage إذا كانت هناك تغييرات
        if (hasChanges) {
          savePasswordsToStorage(updated);
        }

        return updated;
      });

    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPassword = (user: AdminUser) => {
    // جميع كلمات المرور محفوظة في newUserPasswords
    return newUserPasswords[user.id] || 'غير محددة';
  };

  const savePasswordsToStorage = (passwords: { [userId: string]: string }) => {
    try {
      localStorage.setItem('userPasswords', JSON.stringify(passwords));
    } catch (error) {
      console.error('Error saving passwords to localStorage:', error);
    }
  };

  const copyCredentials = async (username: string, password: string) => {
    const text = `اسم المستخدم: ${username}\nكلمة المرور: ${password}`;
    try {
      await navigator.clipboard.writeText(text);
      setCopiedUser(username);
      setTimeout(() => setCopiedUser(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleChangePassword = async (userId: string) => {
    if (!newPassword.trim()) {
      alert('يرجى إدخال كلمة مرور جديدة');
      return;
    }

    try {
      const success = await changeUserPassword(userId, newPassword);
      if (success) {
        alert('تم تغيير كلمة المرور بنجاح');

        // تحديث كلمة المرور في القائمة المحلية
        setNewUserPasswords(prev => {
          const updated = {
            ...prev,
            [userId]: newPassword
          };
          savePasswordsToStorage(updated);
          return updated;
        });

        setEditingPassword(null);
        setNewPassword('');
        await loadUsers(); // إعادة تحميل البيانات
      } else {
        alert('فشل في تغيير كلمة المرور');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      alert('حدث خطأ أثناء تغيير كلمة المرور');
    }
  };

  const handleDeleteUser = async (userId: string, username: string) => {
    if (!confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟`)) {
      return;
    }

    try {
      const success = await deleteUser(userId);
      if (success) {
        alert('تم حذف المستخدم بنجاح');
        // إزالة كلمة المرور المحفوظة
        setNewUserPasswords(prev => {
          const updated = { ...prev };
          delete updated[userId];
          savePasswordsToStorage(updated);
          return updated;
        });
        await loadUsers(); // إعادة تحميل البيانات
      } else {
        alert('فشل في حذف المستخدم');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('حدث خطأ أثناء حذف المستخدم');
    }
  };

  const handleUserCreated = (user: AdminUser, password: string) => {
    // حفظ كلمة المرور بشكل دائم لعرضها
    setNewUserPasswords(prev => {
      const updated = {
        ...prev,
        [user.id]: password
      };
      savePasswordsToStorage(updated);
      return updated;
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="بيانات تسجيل الدخول" 
          description="جدول بيانات المستخدمين وكلمات المرور"
        />
        <div className="animate-pulse">
          <Card>
            <CardContent className="p-6">
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="بيانات تسجيل الدخول"
        description="جدول بيانات المستخدمين وكلمات المرور"
      />

      {/* معلومات النظام */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-green-50">
                <Database className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">مصدر البيانات</p>
                <p className="text-sm text-gray-600">قاعدة بيانات Supabase</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-blue-50">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">الأمان</p>
                <p className="text-sm text-gray-600">كلمات مرور مشفرة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* جدول بيانات تسجيل الدخول */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Key className="h-5 w-5" />
              <span>جدول بيانات تسجيل الدخول</span>
            </CardTitle>

            {/* الأزرار فوق الجدول */}
            <div className="flex items-center space-x-2 space-x-reverse">
              {/* معلومات تشخيص مؤقتة */}
              <div className="text-xs text-gray-500 mr-4">
                المستخدم: {currentUser?.username || 'غير مسجل'} |
                الدور: {currentUser?.role || 'غير محدد'} |
                إداري: {userIsAdmin ? 'نعم' : 'لا'}
              </div>

              {/* زر إضافة مستخدم */}
              <Button
                onClick={() => setShowNewUserForm(true)}
                className="flex items-center space-x-2 space-x-reverse bg-green-600 hover:bg-green-700"
              >
                <User className="h-4 w-4" />
                <span>إضافة مستخدم</span>
              </Button>

              {/* زر إظهار/إخفاء كلمات المرور للإداريين */}
              {userIsAdmin && (
                <Button
                  variant="outline"
                  onClick={() => setShowPasswords(!showPasswords)}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span>{showPasswords ? 'إخفاء كلمات المرور' : 'إظهار كلمات المرور'}</span>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    اسم المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    كلمة المرور
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الاسم الكامل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الدور
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    إجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => {
                  const password = getPassword(user);
                  return (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {user.username}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">
                          {userIsAdmin ? (
                            showPasswords ? (
                              <code className="bg-gray-100 px-3 py-1 rounded text-sm font-mono">
                                {password}
                              </code>
                            ) : (
                              <span className="text-gray-400">••••••••</span>
                            )
                          ) : user.id === currentUser?.id ? (
                            <span className="text-blue-600 text-xs">كلمة مرورك</span>
                          ) : (
                            <span className="text-gray-400">مخفية</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {user.full_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge 
                          variant={user.role === 'admin' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {user.role}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge 
                          variant={user.is_active ? "default" : "destructive"}
                          className="text-xs"
                        >
                          {user.is_active ? 'نشط' : 'معطل'}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {/* نسخ البيانات - للإداريين فقط أو للمستخدم نفسه */}
                          {(userIsAdmin || user.id === currentUser?.id) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyCredentials(user.username, password)}
                              className="flex items-center space-x-1 space-x-reverse"
                            >
                              {copiedUser === user.username ? (
                                <>
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                  <span className="text-green-600">تم النسخ</span>
                                </>
                              ) : (
                                <>
                                  <Copy className="h-3 w-3" />
                                  <span>نسخ</span>
                                </>
                              )}
                            </Button>
                          )}

                          {/* تغيير كلمة المرور - للإداريين أو للمستخدم نفسه */}
                          {(userIsAdmin || user.id === currentUser?.id) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingPassword(user.id)}
                              className="flex items-center space-x-1 space-x-reverse"
                            >
                              <Key className="h-3 w-3" />
                              <span>تغيير كلمة المرور</span>
                            </Button>
                          )}

                          {/* حذف المستخدم - للإداريين فقط وليس نفسه */}
                          {userIsAdmin && user.id !== currentUser?.id && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id, user.username)}
                              className="flex items-center space-x-1 space-x-reverse"
                            >
                              <span>حذف</span>
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* ملاحظة أمنية */}
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2 space-x-reverse">
              <Shield className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">ملاحظة:</p>
                <p>
                  كلمات المرور المعروضة هنا تشمل كلمات المرور الافتراضية للمستخدمين الموجودين
                  وكلمات المرور الحقيقية للمستخدمين الجدد.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* نموذج تغيير كلمة المرور */}
      {editingPassword && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse text-blue-900">
              <Key className="h-5 w-5" />
              <span>تغيير كلمة المرور</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الجديدة
                </label>
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="أدخل كلمة المرور الجديدة"
                />
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Button
                  onClick={() => handleChangePassword(editingPassword)}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>حفظ</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingPassword(null);
                    setNewPassword('');
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* نموذج إضافة مستخدم جديد */}
      {showNewUserForm && (
        <AddUserForm
          onClose={() => setShowNewUserForm(false)}
          onUserAdded={loadUsers}
          onUserCreated={handleUserCreated}
        />
      )}
    </div>
  );
}


'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Trash2, AlertTriangle, FileText, GitMerge, Loader2, Archive } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';

// Declare global types for PDF-lib and PDF.js
declare global {
  interface Window {
    PDFLib: any;
    pdfjsLib: any;
  }
}

export function SplitPdfTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [libsLoaded, setLibsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressText, setProgressText] = useState('');
  const [compressOutput, setCompressOutput] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

   useEffect(() => {
    const loadScripts = async () => {
      try {
        setIsProcessing(true);
        setProgressText('جاري تحميل المكتبات...');
        if (!window.PDFLib) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js';
            script.onload = () => resolve();
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        if (!window.pdfjsLib) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = () => {
              if (window.pdfjsLib) {
                  window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
              }
              resolve();
            };
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        setLibsLoaded(true);
      } catch (err) {
        setError('فشل في تحميل المكتبات اللازمة. يرجى إعادة تحميل الصفحة.');
      } finally {
        setIsProcessing(false);
        setProgressText('');
      }
    };
    loadScripts();
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setError('يرجى اختيار ملف PDF صالح.');
      return;
    }

    if (file.size > 100 * 1024 * 1024) { // 100MB limit
      setError('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 100 ميجابايت.');
      return;
    }
    
    setSelectedFile(file);
    setError(null);
    setSuccess(null);
  };

  const clearFile = () => {
    setSelectedFile(null);
    setError(null);
    setSuccess(null);
    setProgress(0);
    setProgressText('');
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  const processAndDownload = async () => {
    if (!selectedFile || !libsLoaded) return;

    setIsProcessing(true);
    setError(null);
    setSuccess(null);
    setProgress(0);
    setProgressText('بدء عملية التقسيم...');

    try {
      const { PDFDocument } = window.PDFLib;
      const { pdfjsLib } = window;
      const existingPdfBytes = await selectedFile.arrayBuffer();
      const pdfDoc = await PDFDocument.load(existingPdfBytes, { ignoreEncryption: true });
      const pageCount = pdfDoc.getPageCount();

      if (pageCount < 2) {
        setError("الملف يحتوي على صفحة واحدة فقط، لا يمكن تقسيمه.");
        setIsProcessing(false);
        return;
      }

      const zip = new JSZip();

      for (let i = 0; i < pageCount; i++) {
        setProgressText(`معالجة صفحة ${i + 1}/${pageCount}...`);
        
        const singlePagePdfDoc = await PDFDocument.create();
        const [copiedPage] = await singlePagePdfDoc.copyPages(pdfDoc, [i]);
        singlePagePdfDoc.addPage(copiedPage);
        let finalPdfBytes = await singlePagePdfDoc.save();

        if (compressOutput) {
          const pdfToLoad = pdfjsLib.getDocument({ data: finalPdfBytes });
          const pdfToCompress = await pdfToLoad.promise;
          const pageToCompress = await pdfToCompress.getPage(1);
          const viewport = pageToCompress.getViewport({ scale: 1.5 });
          
          const canvas = document.createElement('canvas');
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          const context = canvas.getContext('2d');
          if (!context) throw new Error('لا يمكن إنشاء سياق للرسم.');

          await pageToCompress.render({ canvasContext: context, viewport }).promise;
          const jpgUrl = canvas.toDataURL('image/jpeg', 0.90);
          const jpgImageBytes = await fetch(jpgUrl).then(res => res.arrayBuffer());

          const compressedPdf = await PDFDocument.create();
          const jpgImage = await compressedPdf.embedJpg(jpgImageBytes);
          const newPage = compressedPdf.addPage([viewport.width, viewport.height]);
          newPage.drawImage(jpgImage, { x: 0, y: 0, width: viewport.width, height: viewport.height });
          finalPdfBytes = await compressedPdf.save();
        }
        
        const fileName = `${selectedFile.name.replace('.pdf', '')}_page_${i + 1}.pdf`;
        zip.file(fileName, finalPdfBytes);

        setProgress(((i + 1) / pageCount) * 100);
      }

      const zipBlob = await zip.generateAsync({ type: 'blob' });
      saveAs(zipBlob, `${selectedFile.name.replace('.pdf', '')}_split.zip`);
      
      setSuccess(`تم تقسيم الملف إلى ${pageCount} ملفات ${compressOutput ? 'وضغطها' : ''} بنجاح!`);
      
    } catch (err) {
      console.error('PDF split/compress error:', err);
      setError('فشل في معالجة الملف. تأكد من أن الملف صالح وغير محمي بكلمة مرور.');
    } finally {
      setIsProcessing(false);
      setProgressText('');
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitMerge className="h-6 w-6 text-primary" />
          أداة تقسيم وضغط PDF
        </CardTitle>
        <CardDescription>
          قسّم ملف PDF إلى صفحات فردية، مع خيار ضغط كل صفحة تلقائيًا.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div 
            className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
        >
          <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر ملف PDF للتقسيم</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الملف هنا أو انقر للاختيار</p>
                <Button>اختر ملف</Button>
              </div>
              <p className="text-sm text-gray-500">الحد الأقصى: 100 ميجابايت</p>
            </div>
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".pdf" className="hidden" />
        </div>

        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}
        {success && <Alert className="border-green-200 bg-green-50"><Archive className="h-4 w-4" /><AlertDescription className="text-green-800">{success}</AlertDescription></Alert>}
        
        {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{progressText}</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
        )}

        {selectedFile && (
            <div className="space-y-4">
               <div className="flex items-center justify-between p-3 bg-muted rounded-lg border">
                  <div>
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">{formatBytes(selectedFile.size)}</p>
                  </div>
                  <Button onClick={clearFile} variant="ghost" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50"><Trash2 className="h-4 w-4" /></Button>
              </div>

              <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                <div className="space-y-0.5">
                  <Label className="text-base font-semibold">ضغط الملفات النهائية</Label>
                  <p className="text-sm text-muted-foreground">
                    تقليل حجم كل صفحة مقسمة (قد يؤثر قليلاً على الجودة).
                  </p>
                </div>
                <Switch
                  checked={compressOutput}
                  onCheckedChange={setCompressOutput}
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <Button onClick={processAndDownload} disabled={isProcessing || !libsLoaded} className="flex-1">
                  {isProcessing ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <GitMerge className="ml-2 h-4 w-4" />}
                  بدء التقسيم
                </Button>
              </div>
            </div>
        )}
      </CardContent>
    </Card>
  );
}

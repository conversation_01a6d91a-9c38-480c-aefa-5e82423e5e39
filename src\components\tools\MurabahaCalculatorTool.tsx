
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Calculator, DollarSign, Calendar, Percent, FileText, Info } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  financingAmount: requiredNumber().positive('مبلغ التمويل يجب أن يكون موجبًا.'),
  profitRate: requiredNumber().min(0, 'هامش الربح لا يمكن أن يكون سالبًا.'),
  financingPeriod: requiredNumber().int().positive('مدة التمويل يجب أن تكون موجبة.'),
  downPayment: requiredNumber().nonnegative('الدفعة المقدمة لا يمكن أن تكون سالبة.').default(0),
}).refine(data => {
    return data.financingAmount > data.downPayment;
}, {
  message: 'الدفعة المقدمة لا يمكن أن تكون أكبر من أو تساوي مبلغ التمويل.',
  path: ['downPayment'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  netFinancing: number;
  totalProfit: number;
  totalAmount: number;
  monthlyPayment: number;
  monthlyProfitRate: string;
}

export function MurabahaCalculatorTool() {
  const [results, setResults] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      financingAmount: 500000,
      profitRate: 4.5,
      financingPeriod: 60,
      downPayment: 0,
    },
  });

  const onSubmit = (data: FormValues) => {
    const { financingAmount, profitRate, financingPeriod, downPayment } = data;
    const netFinancing = financingAmount - downPayment;
    const years = financingPeriod / 12;
    const totalProfit = netFinancing * (profitRate / 100) * years;
    const totalAmount = netFinancing + totalProfit;
    const monthlyPayment = totalAmount / financingPeriod;
    const monthlyProfitRate = (profitRate / 12).toFixed(3);
    
    setResults({
      netFinancing,
      totalProfit,
      totalAmount,
      monthlyPayment,
      monthlyProfitRate,
    });
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2,
      numberingSystem: 'latn' // Use Latin numerals (1, 2, 3)
    }).format(num);
  };
  
  return (
    <div className="w-full">
        <div className="text-center mb-8">
            <div className="flex justify-center items-center mb-4">
                <Calculator className="w-10 h-10 text-primary" />
                <h1 className="text-3xl font-bold text-gray-800 mr-3">حاسبة المرابحة الإسلامية</h1>
            </div>
            <p className="text-muted-foreground text-lg">احسب تمويلك المتوافق مع الشريعة الإسلامية</p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="w-6 h-6 text-primary" />
                        بيانات التمويل
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField control={form.control} name="financingAmount" render={({ field }) => (
                                <FormItem><FormLabel><DollarSign className="inline w-4 h-4 ml-1" />مبلغ التمويل (ريال سعودي)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                            )}/>
                            <FormField control={form.control} name="downPayment" render={({ field }) => (
                                <FormItem><FormLabel><DollarSign className="inline w-4 h-4 ml-1" />الدفعة المقدمة (اختياري)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                            )}/>
                            <FormField control={form.control} name="profitRate" render={({ field }) => (
                                <FormItem>
                                    <FormLabel><Percent className="inline w-4 h-4 ml-1" />هامش الربح السنوي (%)</FormLabel>
                                    <FormControl><Input type="number" step="0.1" {...field} /></FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}/>
                             <FormField control={form.control} name="financingPeriod" render={({ field }) => (
                                <FormItem>
                                    <FormLabel><Calendar className="inline w-4 h-4 ml-1" />مدة التمويل (بالأشهر)</FormLabel>
                                    <Select onValueChange={(value) => field.onChange(Number(value))} defaultValue={String(field.value)}>
                                        <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                                        <SelectContent>
                                            <SelectItem value="12">سنة واحدة (12 شهر)</SelectItem>
                                            <SelectItem value="24">سنتان (24 شهر)</SelectItem>
                                            <SelectItem value="36">3 سنوات (36 شهر)</SelectItem>
                                            <SelectItem value="48">4 سنوات (48 شهر)</SelectItem>
                                            <SelectItem value="60">5 سنوات (60 شهر)</SelectItem>
                                            <SelectItem value="120">10 سنوات (120 شهر)</SelectItem>
                                            <SelectItem value="180">15 سنة (180 شهر)</SelectItem>
                                            <SelectItem value="240">20 سنة (240 شهر)</SelectItem>
                                            <SelectItem value="300">25 سنة (300 شهر)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                             )}/>
                             <Button type="submit" className="w-full !mt-6">
                                <Calculator className="ml-2 h-4 w-4"/>
                                احسب
                             </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>
            
            <div className="space-y-4">
                {results ? (
                  <>
                    <Card className="bg-primary/10 border-primary">
                        <CardHeader className="text-center pb-2">
                           <CardTitle className="text-lg font-medium text-primary">القسط الشهري</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                            <div className="text-4xl font-bold text-primary">{formatNumber(results.monthlyPayment)} ريال</div>
                        </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="bg-muted p-3 rounded-md text-center"><p className="text-sm text-muted-foreground">صافي التمويل</p><p className="font-bold text-lg">{formatNumber(results.netFinancing)} ريال</p></div>
                            <div className="bg-muted p-3 rounded-md text-center"><p className="text-sm text-muted-foreground">إجمالي الربح</p><p className="font-bold text-lg text-blue-600">{formatNumber(results.totalProfit)} ريال</p></div>
                        </div>
                        <div className="bg-muted p-4 rounded-md text-center border-t-2 border-primary">
                            <p className="text-md text-muted-foreground font-semibold">إجمالي المبلغ المستحق</p>
                            <p className="text-2xl font-bold text-primary">{formatNumber(results.totalAmount)} ريال</p>
                        </div>
                      </CardContent>
                    </Card>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>ملاحظات هامة</AlertTitle>
                      <AlertDescription>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          <li>هذه الحسابات تقديرية وقد تختلف حسب البنك.</li>
                          <li>لا تشمل الرسوم الإدارية أو رسوم التأمين.</li>
                          <li>معدل الربح الشهري المستخدم في الحساب: {results.monthlyProfitRate}%.</li>
                        </ul>
                      </AlertDescription>
                    </Alert>
                  </>
                ) : (
                    <Card className="flex flex-col items-center justify-center min-h-[400px] bg-muted/50 border-2 border-dashed">
                        <CardHeader className="text-center">
                            <CardTitle>النتائج ستظهر هنا</CardTitle>
                            <CardDescription>أدخل بيانات التمويل ثم اضغط على زر "احسب".</CardDescription>
                        </CardHeader>
                    </Card>
                )}
            </div>
        </div>
    </div>
  );
}

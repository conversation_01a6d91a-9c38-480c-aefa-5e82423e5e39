'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from '@/components/PageHeader';
import { 
  Users, 
  UserPlus, 
  Shield, 
  Calendar,
  Mail,
  User,
  MoreVertical,
  Eye,
  EyeOff
} from 'lucide-react';
import { AdminUser, getAllUsers, updateUserStatus } from '@/lib/auth-supabase';
import { getAllStaticUsers, updateStaticUserStatus, getAuthInfo } from '@/lib/auth-static';
import { useAuth } from '@/components/AuthProvider';

export default function AdminUsersPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const { user: currentUser } = useAuth();

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      const usersData = await getAllUsers();
      setUsers(usersData);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const success = await updateUserStatus(userId, !currentStatus);
      if (success) {
        await loadUsers(); // إعادة تحميل القائمة
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="إدارة المستخدمين" 
          description="إدارة المستخدمين الإداريين للنظام"
        />
        <div className="grid gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="إدارة المستخدمين" 
        description="إدارة المستخدمين الإداريين للنظام"
        action={
          <Button className="flex items-center space-x-2 space-x-reverse">
            <UserPlus className="h-4 w-4" />
            <span>إضافة مستخدم جديد</span>
          </Button>
        }
      />

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  إجمالي المستخدمين
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {users.length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-50">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  المستخدمين النشطين
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {users.filter(u => u.is_active).length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  المستخدمين المعطلين
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {users.filter(u => !u.is_active).length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-red-50">
                <EyeOff className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* قائمة المستخدمين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Users className="h-5 w-5" />
            <span>قائمة المستخدمين</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div 
                key={user.id} 
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-2 rounded-full bg-blue-100">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  
                  <div>
                    <div className="flex items-center space-x-2 space-x-reverse mb-1">
                      <h3 className="font-medium text-gray-900">
                        {user.full_name}
                      </h3>
                      {user.id === currentUser?.id && (
                        <Badge variant="secondary" className="text-xs">
                          أنت
                        </Badge>
                      )}
                      <Badge 
                        variant={user.is_active ? "default" : "destructive"}
                        className="text-xs"
                      >
                        {user.is_active ? 'نشط' : 'معطل'}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <User className="h-3 w-3" />
                        <span>{user.username}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Mail className="h-3 w-3" />
                        <span>{user.email}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Calendar className="h-3 w-3" />
                        <span>انضم في {formatDate(user.created_at)}</span>
                      </div>
                    </div>
                    
                    {user.last_login && (
                      <p className="text-xs text-gray-500 mt-1">
                        آخر تسجيل دخول: {formatDate(user.last_login)}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  {user.id !== currentUser?.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleUserStatus(user.id, user.is_active)}
                      className="flex items-center space-x-1 space-x-reverse"
                    >
                      {user.is_active ? (
                        <>
                          <EyeOff className="h-3 w-3" />
                          <span>تعطيل</span>
                        </>
                      ) : (
                        <>
                          <Eye className="h-3 w-3" />
                          <span>تفعيل</span>
                        </>
                      )}
                    </Button>
                  )}
                  
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

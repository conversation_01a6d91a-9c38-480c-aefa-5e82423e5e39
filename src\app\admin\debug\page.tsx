'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from '@/components/PageHeader';
import { useAuth } from '@/components/AuthProvider';
import { isAdmin } from '@/lib/auth-supabase';

export default function DebugPage() {
  const { user, isAuthenticated } = useAuth();
  const userIsAdmin = isAdmin(user);

  return (
    <div className="space-y-6">
      <PageHeader 
        title="صفحة التشخيص" 
        description="معلومات المستخدم الحالي والصلاحيات"
      />

      <Card>
        <CardHeader>
          <CardTitle>معلومات المصادقة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="font-medium">حالة المصادقة:</p>
              <Badge variant={isAuthenticated ? "default" : "destructive"}>
                {isAuthenticated ? 'مسجل دخول' : 'غير مسجل دخول'}
              </Badge>
            </div>

            <div>
              <p className="font-medium">بيانات المستخدم:</p>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>

            <div>
              <p className="font-medium">هل المستخدم إداري؟</p>
              <Badge variant={userIsAdmin ? "default" : "secondary"}>
                {userIsAdmin ? 'نعم - إداري' : 'لا - مستخدم عادي'}
              </Badge>
            </div>

            <div>
              <p className="font-medium">دور المستخدم:</p>
              <Badge variant="outline">
                {user?.role || 'غير محدد'}
              </Badge>
            </div>

            <div>
              <p className="font-medium">نتيجة فحص isAdmin:</p>
              <code className="bg-gray-100 px-2 py-1 rounded">
                isAdmin(user) = {String(userIsAdmin)}
              </code>
            </div>

            <div>
              <p className="font-medium">شروط الإدارة:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>المستخدم موجود: {user ? '✅' : '❌'}</li>
                <li>الدور = 'admin': {user?.role === 'admin' ? '✅' : '❌'}</li>
                <li>isAdmin() function: {userIsAdmin ? '✅' : '❌'}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

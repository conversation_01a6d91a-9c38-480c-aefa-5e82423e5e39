
'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, Alert<PERSON>riangle, ArrowRightLeft, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';

export function PngToJpgTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quality, setQuality] = useState(0.92); // 92% quality for good balance
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'image/png') {
      setError('يرجى اختيار ملف بصيغة PNG.');
      return;
    }
    
    setSelectedFile(file);
    setError(null);
    setConvertedImage(null);
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const convertImage = () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setError(null);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.src = e.target?.result as string;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          setError('فشل في إنشاء سياق الرسم.');
          setIsProcessing(false);
          return;
        }
        // Fill background with white for transparency
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
        
        try {
          const jpgUrl = canvas.toDataURL('image/jpeg', quality);
          setConvertedImage(jpgUrl);
          toast({ title: 'تم تحويل الصورة بنجاح!' });
        } catch (err) {
          setError('فشل في تحويل الصورة. يرجى المحاولة مرة أخرى.');
        } finally {
          setIsProcessing(false);
        }
      };
    };
    reader.readAsDataURL(selectedFile);
  };

  const clearFile = () => {
    setSelectedFile(null);
    setError(null);
    setConvertedImage(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل من PNG إلى JPG</CardTitle>
        <CardDescription>حوّل صور PNG إلى JPG لتقليل حجم الملف وجعلها مناسبة للويب.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر صورة PNG للتحويل</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
                <Button>اختر صورة</Button>
              </div>
            </div>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/png" className="hidden" />
        </div>
        
        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {selectedFile && (
          <div className="space-y-4">
             <div className="space-y-2">
              <Label htmlFor="quality-slider">جودة الصورة الناتجة ({(quality * 100).toFixed(0)}%)</Label>
              <Slider
                id="quality-slider"
                defaultValue={[quality * 100]}
                max={100}
                step={1}
                onValueChange={(value) => setQuality(value[0] / 100)}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
                <Button onClick={convertImage} disabled={isProcessing} className="flex-1">
                {isProcessing ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <ArrowRightLeft className="ml-2 h-4 w-4" />}
                {isProcessing ? 'جاري التحويل...' : 'تحويل إلى JPG'}
                </Button>
                <Button onClick={clearFile} variant="outline" disabled={isProcessing} className="flex-1"><Trash2 className="ml-2 h-4 w-4" /> مسح</Button>
            </div>
          </div>
        )}
        
        {convertedImage && (
          <div className="mt-6 text-center space-y-4">
             <h3 className="font-semibold">الصورة المحولة (JPG)</h3>
            <img src={convertedImage} alt="Converted" className="max-w-full rounded-md border" />
            <Button onClick={() => saveAs(convertedImage, `${selectedFile?.name.split('.')[0]}.jpg`)} className="w-full">
              <Download className="ml-2 h-4 w-4" />
              تحميل الصورة
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

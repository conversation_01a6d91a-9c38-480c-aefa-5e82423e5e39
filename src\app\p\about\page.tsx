import { Metadata } from 'next';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calculator, Clock, Users, Shield, Heart, Zap } from 'lucide-react';

export const metadata: Metadata = {
  title: 'من نحن - جامع الأدوات',
  description: 'تعرف على جامع الأدوات، منصتك الشاملة للحاسبات والمحولات والأدوات المفيدة باللغة العربية. نقدم أدوات مجانية وسهلة الاستخدام لجميع احتياجاتك اليومية.',
  keywords: ['من نحن', 'جامع الأدوات', 'حاسبات عربية', 'أدوات مجانية', 'محولات', 'خدمات رقمية'],
};

const features = [
  {
    icon: Calculator,
    title: 'أدوات شاملة',
    description: 'أكثر من 80 أداة مختلفة تغطي جميع احتياجاتك من الحاسبات والمحولات والأدوات المفيدة'
  },
  {
    icon: Clock,
    title: 'سرعة وسهولة',
    description: 'جميع أدواتنا مصممة لتكون سريعة وسهلة الاستخدام مع واجهة بسيطة ومفهومة'
  },
  {
    icon: Users,
    title: 'مجتمع عربي',
    description: 'نخدم المجتمع العربي بأدوات مصممة خصيصاً للثقافة واللغة العربية'
  },
  {
    icon: Shield,
    title: 'أمان وخصوصية',
    description: 'نحترم خصوصيتك ولا نحفظ أي بيانات شخصية. جميع العمليات تتم محلياً في متصفحك'
  },
  {
    icon: Heart,
    title: 'مجاني تماماً',
    description: 'جميع أدواتنا مجانية بالكامل ولا تتطلب تسجيل أو اشتراك'
  },
  {
    icon: Zap,
    title: 'تحديث مستمر',
    description: 'نضيف أدوات جديدة ونحسن الموجودة باستمرار بناءً على احتياجات المستخدمين'
  }
];

const stats = [
  { number: '80+', label: 'أداة مفيدة' },
  { number: '12', label: 'فئة مختلفة' },
  { number: '100%', label: 'مجاني' },
  { number: '24/7', label: 'متاح دائماً' }
];

export default async function AboutPage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="من نحن" 
        description="تعرف على جامع الأدوات ورؤيتنا في تقديم أفضل الأدوات الرقمية للمجتمع العربي"
      />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 mb-12">
        <h2 className="text-3xl font-headline font-bold mb-4 text-center">
          منصتك الشاملة للأدوات الرقمية
        </h2>
        <p className="text-lg text-muted-foreground text-center max-w-3xl mx-auto leading-relaxed">
          جامع الأدوات هو موقع عربي متخصص في تقديم مجموعة شاملة من الحاسبات والمحولات والأدوات المفيدة. 
          نهدف إلى تسهيل حياتك اليومية من خلال أدوات بسيطة وفعالة ومجانية بالكامل.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">{stat.number}</div>
            <div className="text-sm text-muted-foreground">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Features */}
      <div className="mb-12">
        <h3 className="text-2xl font-headline font-bold mb-8 text-center">
          لماذا تختار جامع الأدوات؟
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Mission */}
      <div className="mb-12">
        <h3 className="text-2xl font-headline font-bold mb-6 text-center">رؤيتنا ورسالتنا</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">رؤيتنا</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                أن نكون المنصة الرائدة في العالم العربي لتقديم الأدوات الرقمية المفيدة والموثوقة، 
                ونساهم في تسهيل الحياة اليومية للمستخدمين من خلال التكنولوجيا البسيطة والفعالة.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">رسالتنا</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                نلتزم بتطوير وتقديم أدوات رقمية عالية الجودة ومجانية بالكامل، مع التركيز على 
                سهولة الاستخدام والدقة والأمان، لخدمة المجتمع العربي وتلبية احتياجاته المتنوعة.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Values */}
      <div className="mb-12">
        <h3 className="text-2xl font-headline font-bold mb-6 text-center">قيمنا</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h4 className="font-bold text-lg mb-2">الجودة</h4>
              <p className="text-muted-foreground">نحرص على تقديم أدوات عالية الجودة ومختبرة بعناية</p>
            </div>
            <div>
              <h4 className="font-bold text-lg mb-2">البساطة</h4>
              <p className="text-muted-foreground">نؤمن بأن الأدوات الجيدة يجب أن تكون بسيطة وسهلة الاستخدام</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <h4 className="font-bold text-lg mb-2">الشفافية</h4>
              <p className="text-muted-foreground">نكون صادقين وواضحين في جميع تعاملاتنا مع المستخدمين</p>
            </div>
            <div>
              <h4 className="font-bold text-lg mb-2">التطوير المستمر</h4>
              <p className="text-muted-foreground">نسعى دائماً لتحسين خدماتنا وإضافة أدوات جديدة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-headline font-bold mb-4">
          هل لديك اقتراح أو استفسار؟
        </h3>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          نحن نرحب بآرائكم واقتراحاتكم لتحسين خدماتنا. تواصلوا معنا ولا تترددوا في مشاركة أفكاركم.
        </p>
        <a
          href="/p/contact"
          className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
        >
          تواصل معنا
        </a>
      </div>
    </div>
  );
}

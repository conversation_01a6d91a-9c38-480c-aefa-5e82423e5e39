
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowRightLeft, Ruler, Weight, Thermometer } from 'lucide-react';

// --- Unit Definitions ---
const units = {
  length: {
    label: 'الطول',
    icon: Ruler,
    options: {
      m: { name: 'متر (m)', factor: 1 },
      km: { name: 'كيلو متر (km)', factor: 1000 },
      cm: { name: 'سنتيمتر (cm)', factor: 0.01 },
      mm: { name: 'مليمتر (mm)', factor: 0.001 },
      in: { name: 'بوصة (in)', factor: 0.0254 },
      ft: { name: 'قدم (ft)', factor: 0.3048 },
      yd: { name: 'ياردة (yd)', factor: 0.9144 },
      mi: { name: 'ميل (mi)', factor: 1609.34 },
    },
  },
  weight: {
    label: 'الوزن',
    icon: Weight,
    options: {
      kg: { name: 'كيلوجرام (kg)', factor: 1 },
      g: { name: 'جرام (g)', factor: 0.001 },
      mg: { name: 'مليجرام (mg)', factor: 0.000001 },
      t: { name: 'طن متري (t)', factor: 1000 },
      lb: { name: 'رطل (lb)', factor: 0.453592 },
      oz: { name: 'أونصة (oz)', factor: 0.0283495 },
    },
  },
  temperature: {
    label: 'الحرارة',
    icon: Thermometer,
    options: {
      c: { name: 'مئوية (°C)', toBase: (val: number) => val, fromBase: (val: number) => val },
      f: { name: 'فهرنهايت (°F)', toBase: (val: number) => (val - 32) * 5 / 9, fromBase: (val: number) => (val * 9 / 5) + 32 },
      k: { name: 'كلفن (K)', toBase: (val: number) => val - 273.15, fromBase: (val: number) => val + 273.15 },
    },
  },
};

type Category = keyof typeof units;

const ConverterForm = ({ categoryKey }: { categoryKey: Category }) => {
  const category = units[categoryKey];
  const unitKeys = Object.keys(category.options);

  const [fromValue, setFromValue] = useState('1');
  const [toValue, setToValue] = useState('');
  const [fromUnit, setFromUnit] = useState(unitKeys[0]);
  const [toUnit, setToUnit] = useState(unitKeys[1] || unitKeys[0]);

  useEffect(() => {
    const calculate = () => {
      const val = parseFloat(fromValue);
      if (isNaN(val)) {
        setToValue('');
        return;
      }

      let result;
      if (categoryKey === 'temperature') {
        const fromFunc = (category.options as any)[fromUnit].toBase;
        const toFunc = (category.options as any)[toUnit].fromBase;
        result = toFunc(fromFunc(val));
      } else {
        const fromFactor = (category.options as any)[fromUnit].factor;
        const toFactor = (category.options as any)[toUnit].factor;
        const baseValue = val * fromFactor;
        result = baseValue / toFactor;
      }
      setToValue(result.toLocaleString('en-US', { maximumFractionDigits: 4, useGrouping: false }));
    };
    calculate();
  }, [fromValue, fromUnit, toUnit, categoryKey, category.options]);

  const handleSwap = () => {
    const currentFromUnit = fromUnit;
    const currentToUnit = toUnit;
    const currentFromValue = fromValue;
    const currentToValue = toValue;
    setFromUnit(currentToUnit);
    setToUnit(currentFromUnit);
    setFromValue(currentToValue);
    setToValue(currentFromValue);
  };
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-[1fr,auto] gap-2 items-center">
        <Input
          type="number"
          value={fromValue}
          onChange={(e) => setFromValue(e.target.value)}
          className="h-12 text-lg"
        />
        <Select value={fromUnit} onValueChange={setFromUnit}>
          <SelectTrigger className="h-12 text-base">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(category.options).map(([key, u]) => (
              <SelectItem key={key} value={key}>{(u as any).name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-center">
         <Button variant="ghost" size="icon" onClick={handleSwap}>
            <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
         </Button>
      </div>
      
      <div className="grid grid-cols-[1fr,auto] gap-2 items-center">
        <Input
          readOnly
          value={toValue}
          onChange={(e) => setToValue(e.target.value)}
          className="h-12 text-lg bg-muted font-bold text-primary"
          dir="ltr"
        />
        <Select value={toUnit} onValueChange={setToUnit}>
          <SelectTrigger className="h-12 text-base">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(category.options).map(([key, u]) => (
              <SelectItem key={key} value={key}>{(u as any).name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};


export function UnitConverterTool() {
  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle>محول الوحدات</CardTitle>
        <CardDescription>تحويل بين وحدات الطول والوزن والحرارة بسهولة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="length" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            {Object.entries(units).map(([key, { label, icon: Icon }]) => (
              <TabsTrigger key={key} value={key}>
                <Icon className="ml-2 h-4 w-4" />
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <TabsContent value="length" className="pt-6">
            <ConverterForm categoryKey="length" />
          </TabsContent>
          <TabsContent value="weight" className="pt-6">
            <ConverterForm categoryKey="weight" />
          </TabsContent>
          <TabsContent value="temperature" className="pt-6">
            <ConverterForm categoryKey="temperature" />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

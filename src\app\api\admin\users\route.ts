import { NextRequest, NextResponse } from 'next/server';
import { getAllUsers, createUser, updateUserStatus } from '@/lib/auth-supabase';

// GET - جلب جميع المستخدمين
export async function GET() {
  try {
    const users = await getAllUsers();
    
    return NextResponse.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب المستخدمين' },
      { status: 500 }
    );
  }
}

// POST - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, email, password, full_name, role } = body;

    // التحقق من البيانات المطلوبة
    if (!username || !email || !password || !full_name) {
      return NextResponse.json(
        { success: false, error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    // إنشاء المستخدم
    const user = await createUser({
      username,
      email,
      password,
      full_name,
      role: role || 'admin'
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'فشل في إنشاء المستخدم' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      user,
      message: 'تم إنشاء المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء المستخدم' },
      { status: 500 }
    );
  }
}

// PATCH - تحديث حالة المستخدم
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, isActive } = body;

    if (!userId || typeof isActive !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'بيانات غير صحيحة' },
        { status: 400 }
      );
    }

    const success = await updateUserStatus(userId, isActive);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'فشل في تحديث حالة المستخدم' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error updating user status:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث حالة المستخدم' },
      { status: 500 }
    );
  }
}

#!/usr/bin/env node

/**
 * أداة إعادة إرسال sitemap إلى Google Search Console
 * تساعد في إعادة فهرسة الموقع بسرعة
 */

const https = require('https');
const fs = require('fs');

const SITE_URL = 'https://adawat.org';

class SitemapResubmitter {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      actions: []
    };
  }

  async resubmitSitemap() {
    console.log('🔄 بدء عملية إعادة إرسال sitemap...');
    
    try {
      // 1. فحص sitemap أولاً
      await this.checkSitemap();
      
      // 2. إرسال ping لـ Google
      await this.pingGoogle();
      
      // 3. إرسال ping لـ Bing
      await this.pingBing();
      
      // 4. إنشاء تقرير
      this.generateReport();
      
    } catch (error) {
      console.error('❌ خطأ في العملية:', error.message);
    }
  }

  checkSitemap() {
    return new Promise((resolve, reject) => {
      console.log('🔍 فحص sitemap...');
      
      https.get(`${SITE_URL}/sitemap.xml`, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          if (res.statusCode === 200) {
            const urlCount = (data.match(/<url>/g) || []).length;
            console.log(`✅ sitemap متاح - يحتوي على ${urlCount} صفحة`);
            
            this.results.actions.push({
              action: 'sitemap_check',
              status: 'success',
              details: `${urlCount} صفحة موجودة`,
              timestamp: new Date().toISOString()
            });
            
            resolve(urlCount);
          } else {
            throw new Error(`sitemap غير متاح - كود الحالة: ${res.statusCode}`);
          }
        });
      }).on('error', reject);
    });
  }

  pingGoogle() {
    return new Promise((resolve, reject) => {
      console.log('📤 إرسال ping لـ Google...');
      
      const pingUrl = `https://www.google.com/ping?sitemap=${encodeURIComponent(SITE_URL + '/sitemap.xml')}`;
      
      https.get(pingUrl, (res) => {
        if (res.statusCode === 200) {
          console.log('✅ تم إرسال ping لـ Google بنجاح');
          
          this.results.actions.push({
            action: 'google_ping',
            status: 'success',
            details: 'تم إرسال sitemap لـ Google',
            timestamp: new Date().toISOString()
          });
          
          resolve();
        } else {
          console.log(`⚠️ Google ping - كود الحالة: ${res.statusCode}`);
          
          this.results.actions.push({
            action: 'google_ping',
            status: 'warning',
            details: `كود الحالة: ${res.statusCode}`,
            timestamp: new Date().toISOString()
          });
          
          resolve();
        }
      }).on('error', (err) => {
        console.error('❌ خطأ في Google ping:', err.message);
        
        this.results.actions.push({
          action: 'google_ping',
          status: 'error',
          details: err.message,
          timestamp: new Date().toISOString()
        });
        
        resolve(); // لا نريد إيقاف العملية
      });
    });
  }

  pingBing() {
    return new Promise((resolve, reject) => {
      console.log('📤 إرسال ping لـ Bing...');
      
      const pingUrl = `https://www.bing.com/ping?sitemap=${encodeURIComponent(SITE_URL + '/sitemap.xml')}`;
      
      https.get(pingUrl, (res) => {
        if (res.statusCode === 200) {
          console.log('✅ تم إرسال ping لـ Bing بنجاح');
          
          this.results.actions.push({
            action: 'bing_ping',
            status: 'success',
            details: 'تم إرسال sitemap لـ Bing',
            timestamp: new Date().toISOString()
          });
          
          resolve();
        } else {
          console.log(`⚠️ Bing ping - كود الحالة: ${res.statusCode}`);
          
          this.results.actions.push({
            action: 'bing_ping',
            status: 'warning',
            details: `كود الحالة: ${res.statusCode}`,
            timestamp: new Date().toISOString()
          });
          
          resolve();
        }
      }).on('error', (err) => {
        console.error('❌ خطأ في Bing ping:', err.message);
        
        this.results.actions.push({
          action: 'bing_ping',
          status: 'error',
          details: err.message,
          timestamp: new Date().toISOString()
        });
        
        resolve(); // لا نريد إيقاف العملية
      });
    });
  }

  generateReport() {
    const report = {
      summary: {
        totalActions: this.results.actions.length,
        successfulActions: this.results.actions.filter(a => a.status === 'success').length,
        timestamp: this.results.timestamp
      },
      actions: this.results.actions,
      nextSteps: [
        'تحقق من Google Search Console خلال 24-48 ساعة',
        'راقب تقارير الفهرسة في Search Console',
        'تأكد من عدم وجود أخطاء في robots.txt',
        'راقب تحسن الظهور والنقرات خلال الأسبوع القادم'
      ]
    };

    // حفظ التقرير
    const reportFile = `sitemap-resubmit-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    // طباعة الملخص
    console.log('\n📊 ملخص عملية إعادة الإرسال:');
    console.log('=====================================');
    console.log(`✅ العمليات الناجحة: ${report.summary.successfulActions}/${report.summary.totalActions}`);
    console.log(`🕒 وقت التنفيذ: ${new Date(report.summary.timestamp).toLocaleString('ar-SA')}`);
    
    console.log('\n📋 الإجراءات المنفذة:');
    this.results.actions.forEach(action => {
      const icon = action.status === 'success' ? '✅' : action.status === 'warning' ? '⚠️' : '❌';
      console.log(`   ${icon} ${action.action}: ${action.details}`);
    });
    
    console.log('\n🎯 الخطوات التالية:');
    report.nextSteps.forEach(step => console.log(`   • ${step}`));
    
    console.log(`\n📄 تقرير مفصل محفوظ في: ${reportFile}`);
    
    return report;
  }

  async scheduleRegularPings() {
    console.log('⏰ جدولة ping دوري كل 6 ساعات...');
    
    // ping فوري
    await this.resubmitSitemap();
    
    // ping كل 6 ساعات
    setInterval(async () => {
      console.log('\n🔄 ping دوري مجدول...');
      await this.resubmitSitemap();
    }, 6 * 60 * 60 * 1000); // 6 ساعات
  }
}

// تشغيل الأداة
const resubmitter = new SitemapResubmitter();

if (process.argv.includes('--schedule')) {
  resubmitter.scheduleRegularPings();
} else {
  resubmitter.resubmitSitemap();
}
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calculator, ArrowLeft, Coins, Calendar, QrCode, Activity, Type, DollarSign, User, Percent, Ruler, MessageCircle, GraduationCap, TrendingUp, Receipt, Tag } from 'lucide-react';

interface Tool {
  name: string;
  slug: string;
  description: string;
}

interface RelatedToolsProps {
  tools: Tool[];
  title?: string;
}

// دالة لاختيار الأيقونة المناسبة لكل أداة
function getToolIcon(slug: string) {
  const iconMap: { [key: string]: any } = {
    'zakat-calculator': Coins,
    'date-converter': Calendar,
    'qr-code-generator': QrCode,
    'bmi-calculator': Activity,
    'word-count': Type,
    'text-repeater': Type,
    'currency-converter': DollarSign,
    'age-calculator': User,
    'investment-calculator': TrendingUp,
    'retirement-calculator': TrendingUp,
    'percentage-calculator': Percent,
    'unit-converter': Ruler,
    'whatsapp-link-generator': MessageCircle,
    'gpa-calculator': GraduationCap,
    'vat-calculator': Receipt,
    'discount-calculator': Tag,
  };

  return iconMap[slug] || Calculator;
}

export function RelatedTools({ tools, title = "الأدوات المرتبطة" }: RelatedToolsProps) {
  if (!tools || tools.length === 0) {
    return null;
  }

  return (
    <Card className="mt-8 bg-gradient-to-br from-primary/5 via-primary/8 to-primary/10 border-primary/20 shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-gradient-to-br from-primary/20 to-primary/30 rounded-xl shadow-md">
            <Calculator className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-primary mb-1">{title}</h3>
            <p className="text-sm text-muted-foreground">
              جرب هذه الأدوات المفيدة المرتبطة بموضوع المقالة
            </p>
          </div>
        </div>
        
        <div className={`grid gap-4 ${
          tools.length === 1
            ? 'grid-cols-1 max-w-lg mx-auto'
            : 'grid-cols-1 md:grid-cols-2'
        }`}>
          {tools.map((tool, index) => {
            const IconComponent = getToolIcon(tool.slug);
            return (
              <div
                key={index}
                className={`group bg-white/50 backdrop-blur-sm border border-primary/20 rounded-lg hover:border-primary hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${
                  tools.length === 1 ? 'p-6' : 'p-5'
                }`}
              >
                <div className="flex items-start gap-3 h-full">
                  <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                    <IconComponent className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg mb-2 text-foreground group-hover:text-primary transition-colors">
                      {tool.name}
                    </h4>
                    <p className="text-muted-foreground text-sm mb-4 leading-relaxed">
                      {tool.description}
                    </p>
                    <Link href={`/tools/${tool.slug}`} className="block">
                      <Button
                        className="w-full bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg transition-all duration-300 font-medium"
                        size="lg"
                      >
                        <ArrowLeft className="h-4 w-4 ml-2 transition-transform group-hover:translate-x-[-2px]" />
                        جرب الأداة الآن
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {tools.length > 2 && (
          <div className="mt-6 text-center">
            <Link href="/tools" className="text-primary hover:underline text-sm font-medium">
              استكشف المزيد من الأدوات المفيدة ←
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

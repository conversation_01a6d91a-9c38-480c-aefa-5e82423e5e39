import { Metadata } from 'next';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/PageHeader';
import { Calendar, Clock, ArrowLeft, Eye, Heart } from 'lucide-react';
import { getArticles } from '@/lib/articles-supabase';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

export const metadata: Metadata = {
  title: 'مقالات ونصائح مفيدة',
  description: 'مجموعة من المقالات والنصائح المفيدة حول استخدام الأدوات المختلفة والحاسبات والمحولات',
  keywords: ['مقالات عربية', 'نصائح', 'شروحات', 'دليل استخدام', 'حاسبات', 'محولات'],
};

// Static articles as fallback
const staticArticles = [
  {
    id: 'how-to-calculate-zakat',
    title: 'كيفية حساب الزكاة بطريقة صحيحة',
    description: 'دليل شامل لحساب زكاة المال والذهب والفضة وفقاً للأحكام الشرعية',
    createdAt: new Date('2024-12-15'),
    readTime: '5 دقائق',
    category: 'إسلامية',
    views: 0,
    likes: 0,
    slug: 'how-to-calculate-zakat',
  },
  {
    id: 'hijri-calendar-guide',
    title: 'دليل شامل للتقويم الهجري والميلادي',
    description: 'تعرف على الفروق بين التقويمين وكيفية التحويل بينهما بدقة',
    createdAt: new Date('2024-12-10'),
    readTime: '7 دقائق',
    category: 'تعليمية',
    views: 0,
    likes: 0,
    slug: 'hijri-calendar-guide',
  },
  {
    id: 'text-tools-productivity',
    title: 'كيف تستخدم أدوات النصوص لزيادة الإنتاجية',
    description: 'نصائح وحيل لاستخدام أدوات النصوص المختلفة لتحسين كتابتك وإنتاجيتك',
    createdAt: new Date('2024-12-05'),
    readTime: '6 دقائق',
    category: 'إنتاجية',
    views: 0,
    likes: 0,
    slug: 'text-tools-productivity',
  },
];

const formatDate = (timestamp: any) => {
  if (!timestamp) return '';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  return formatDistanceToNow(date, { addSuffix: true, locale: ar });
};

export default async function ArticlesPage() {
  // Fetch published articles from database
  let articles = [];

  try {
    const { articles: dbArticles } = await getArticles({ status: 'published', limit: 20 });
    articles = dbArticles;
  } catch (error) {
    console.error('Error loading articles:', error);
    // Fallback to static articles if database fails
    articles = staticArticles;
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <PageHeader 
        title="مقالات ونصائح مفيدة" 
        description="مجموعة من المقالات والنصائح المفيدة حول استخدام الأدوات المختلفة والحاسبات والمحولات"
      />

      {articles.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-muted-foreground">لا توجد مقالات متاحة حالياً</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {articles.map((article) => (
            <Card key={article.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between gap-2">
                  <CardTitle className="text-lg leading-tight">
                    {article.title}
                  </CardTitle>
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full whitespace-nowrap">
                    {article.category}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground text-sm line-clamp-3">
                  {article.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(article.createdAt)}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {article.readTime}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <span className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {article.views || 0}
                    </span>
                    <span className="flex items-center gap-1">
                      <Heart className="h-3 w-3" />
                      {article.likes || 0}
                    </span>
                  </div>
                </div>
                
                <Link 
                  href={`/articles/${article.slug}`}
                  className="inline-flex items-center gap-2 text-primary hover:underline text-sm font-medium"
                >
                  اقرأ المزيد
                  <ArrowLeft className="h-3 w-3" />
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}


'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const GALLON_TO_LITER_FACTOR = 3.78541;

export function GallonToLiterConverterTool() {
  const [gallons, setGallons] = useState('1');
  const [liters, setLiters] = useState(GALLON_TO_LITER_FACTOR.toString());

  const handleGallonsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGallons(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setLiters((numValue * GALLON_TO_LITER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 3, useGrouping: false}));
    } else {
      setLiters('');
    }
  };

  const handleLitersChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLiters(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setGallons((numValue / GALLON_TO_LITER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 3, useGrouping: false}));
    } else {
      setGallons('');
    }
  };
  
  const handleSwap = () => {
    const currentGallons = gallons;
    const currentLiters = liters;
    setGallons(currentLiters);
    setLiters(currentGallons);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من جالون إلى لتر (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري. (يتم استخدام الجالون الأمريكي).
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 جالون أمريكي = 3.78541 لتر
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="gallons" className="text-sm font-medium mb-2 block">
              جالون (أمريكي)
            </label>
            <Input
              id="gallons"
              type="number"
              value={gallons}
              onChange={handleGallonsChange}
              placeholder="أدخل الحجم بالجالون"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="liters" className="text-sm font-medium mb-2 block">
              لتر (Liter)
            </label>
            <Input
              id="liters"
              type="number"
              value={liters}
              onChange={handleLitersChange}
              placeholder="أدخل الحجم باللتر"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/PageHeader';
import { 
  BarChart3, 
  FileText, 
  Eye, 
  Heart, 
  TrendingUp,
  Users,
  Calendar,
  Target
} from 'lucide-react';
import { ArticleStats } from '@/types/article';

export default function AdminStatsPage() {
  const [stats, setStats] = useState<ArticleStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const response = await fetch('/api/articles/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="إحصائيات المقالات" 
          description="نظرة عامة على أداء المقالات والمحتوى"
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="إحصائيات المقالات" 
          description="نظرة عامة على أداء المقالات والمحتوى"
        />
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-gray-500">فشل في تحميل الإحصائيات</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statCards = [
    {
      title: 'إجمالي المقالات',
      value: stats.totalArticles,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'المقالات المنشورة',
      value: stats.publishedArticles,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'المقالات المسودة',
      value: stats.draftArticles,
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'إجمالي المشاهدات',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'إجمالي الإعجابات',
      value: stats.totalLikes.toLocaleString(),
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    }
  ];

  return (
    <div className="space-y-6">
      <PageHeader 
        title="إحصائيات المقالات" 
        description="نظرة عامة على أداء المقالات والمحتوى"
      />

      {/* البطاقات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* إحصائيات إضافية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <BarChart3 className="h-5 w-5" />
              <span>إحصائيات إضافية</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  المقالات المسودة
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.draftArticles} مقال
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  عدد الفئات
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.categoriesCount} فئة
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  عدد العلامات
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.tagsCount} علامة
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Target className="h-5 w-5" />
              <span>معدلات الأداء</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  متوسط المشاهدات لكل مقال
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.publishedArticles > 0 
                    ? Math.round(stats.totalViews / stats.publishedArticles).toLocaleString()
                    : 0
                  }
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  متوسط الإعجابات لكل مقال
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.publishedArticles > 0 
                    ? Math.round(stats.totalLikes / stats.publishedArticles).toLocaleString()
                    : 0
                  }
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">
                  معدل التفاعل
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {stats.totalViews > 0 
                    ? ((stats.totalLikes / stats.totalViews) * 100).toFixed(1)
                    : 0
                  }%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

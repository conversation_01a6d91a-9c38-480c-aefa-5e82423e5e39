import { NextRequest, NextResponse } from 'next/server';
import { createUser, deleteUser, getAllUsers } from '@/lib/auth-supabase';

export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'create':
        const newUser = await createUser({
          username: data.username,
          email: data.email,
          fullName: data.fullName,
          password: data.password,
          role: data.role || 'editor'
        });
        
        if (newUser) {
          return NextResponse.json({ 
            success: true, 
            message: 'تم إنشاء المستخدم بنجاح',
            user: newUser 
          });
        } else {
          return NextResponse.json({ 
            success: false, 
            message: 'فشل في إنشاء المستخدم' 
          }, { status: 400 });
        }

      case 'delete':
        const deleteSuccess = await deleteUser(data.userId);
        
        if (deleteSuccess) {
          return NextResponse.json({ 
            success: true, 
            message: 'تم حذف المستخدم بنجاح' 
          });
        } else {
          return NextResponse.json({ 
            success: false, 
            message: 'فشل في حذف المستخدم' 
          }, { status: 400 });
        }

      case 'list':
        const users = await getAllUsers();
        return NextResponse.json({ 
          success: true, 
          users 
        });

      default:
        return NextResponse.json({ 
          success: false, 
          message: 'عملية غير مدعومة' 
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Test user operations error:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'حدث خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const users = await getAllUsers();
    return NextResponse.json({ 
      success: true, 
      users,
      count: users.length 
    });
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'حدث خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

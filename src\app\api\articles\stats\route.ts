import { NextResponse } from 'next/server';
import { getArticleStats } from '@/lib/articles-supabase';

export async function GET() {
  try {
    const stats = await getArticleStats();
    return NextResponse.json({ stats });
  } catch (error) {
    console.error('Error in GET /api/articles/stats:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإحصائيات' },
      { status: 500 }
    );
  }
}

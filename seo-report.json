{"timestamp": "2025-07-26T18:35:27.368Z", "status": {"homePage": {"url": "https://adawat.org", "statusCode": 200, "loadTime": 545, "accessible": true, "size": 282983, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:35:27.916Z"}, "sitemap": {"accessible": true, "urlCount": 322, "size": 52199, "lastModified": "<PERSON>ي<PERSON> محدد"}, "robots": {"accessible": true, "hasSitemap": true, "allowsAll": true, "size": 1961}, "importantPages": {"/tools/zakat-calculator": {"url": "https://adawat.org/tools/zakat-calculator", "statusCode": 200, "loadTime": 71, "accessible": true, "size": 20445, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:35:28.131Z"}, "/tools/date-converter": {"url": "https://adawat.org/tools/date-converter", "statusCode": 200, "loadTime": 72, "accessible": true, "size": 20412, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:35:28.203Z"}, "/tools/age-calculator": {"url": "https://adawat.org/tools/age-calculator", "statusCode": 200, "loadTime": 72, "accessible": true, "size": 20406, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:35:28.275Z"}, "/tools/currency-converter": {"url": "https://adawat.org/tools/currency-converter", "statusCode": 200, "loadTime": 72, "accessible": true, "size": 20449, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:35:28.347Z"}}}, "performance": {}, "issues": ["الصفحة الرئيسية لا تحتوي على title", "الصفحة الرئيسية لا تحتوي على meta description"], "recommendations": ["جميع الصفحات المهمة متاحة ✅", "ركز على حل المشاكل التقنية أولاً"]}
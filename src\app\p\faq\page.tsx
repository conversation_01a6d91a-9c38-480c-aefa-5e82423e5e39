import { Metadata } from 'next';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { HelpCircle, Calculator, Shield, Smartphone, Clock, Zap } from 'lucide-react';

export const metadata: Metadata = {
  title: 'الأسئلة الشائعة - جامع الأدوات',
  description: 'إجابات على الأسئلة الأكثر شيوعاً حول استخدام أدوات جامع الأدوات، الخصوصية، الأمان، والمزيد من المعلومات المفيدة.',
  keywords: ['أسئلة شائعة', 'مساعدة', 'دعم فني', 'كيفية الاستخدام', 'جامع الأدوات', 'حاسبات'],
};

const faqCategories = [
  {
    title: 'عام',
    icon: HelpCircle,
    questions: [
      {
        question: 'ما هو جامع الأدوات؟',
        answer: 'جامع الأدوات هو موقع عربي يقدم مجموعة شاملة من الحاسبات والمحولات والأدوات المفيدة مجاناً. نهدف إلى تسهيل حياتك اليومية من خلال أدوات بسيطة وفعالة.'
      },
      {
        question: 'هل جميع الأدوات مجانية؟',
        answer: 'نعم، جميع الأدوات المتوفرة على موقعنا مجانية بالكامل ولا تتطلب أي رسوم أو اشتراك أو تسجيل.'
      },
      {
        question: 'كم عدد الأدوات المتوفرة؟',
        answer: 'يحتوي الموقع على أكثر من 80 أداة مختلفة موزعة على 12 فئة، ونضيف أدوات جديدة بانتظام.'
      },
      {
        question: 'هل أحتاج لإنشاء حساب؟',
        answer: 'لا، جميع الأدوات متاحة للاستخدام المباشر دون الحاجة لإنشاء حساب أو تسجيل الدخول.'
      }
    ]
  },
  {
    title: 'الاستخدام',
    icon: Calculator,
    questions: [
      {
        question: 'كيف أستخدم الحاسبات؟',
        answer: 'ببساطة ادخل إلى الأداة المطلوبة، أدخل البيانات المطلوبة في الحقول المخصصة، واضغط على زر الحساب للحصول على النتيجة فوراً.'
      },
      {
        question: 'هل النتائج دقيقة؟',
        answer: 'نعم، جميع الحاسبات مبرمجة وفقاً للمعايير والصيغ الرياضية الصحيحة ومختبرة بعناية لضمان دقة النتائج.'
      },
      {
        question: 'ماذا لو أدخلت بيانات خاطئة؟',
        answer: 'يمكنك تعديل البيانات في أي وقت وإعادة الحساب. كما أن معظم الأدوات تتضمن تحقق من صحة البيانات المدخلة.'
      },
      {
        question: 'هل يمكنني حفظ النتائج؟',
        answer: 'يمكنك نسخ النتائج أو طباعة الصفحة. بعض الأدوات تتيح تنزيل النتائج كملف PDF.'
      }
    ]
  },
  {
    title: 'الخصوصية والأمان',
    icon: Shield,
    questions: [
      {
        question: 'هل تحفظون بياناتي؟',
        answer: 'لا، نحن لا نحفظ أي بيانات شخصية أو معلومات تدخلونها في الأدوات. جميع العمليات تتم محلياً في متصفحكم.'
      },
      {
        question: 'هل الموقع آمن؟',
        answer: 'نعم، الموقع يستخدم بروتوكول HTTPS المشفر ولا يجمع أي معلومات شخصية. خصوصيتكم مهمة جداً بالنسبة لنا.'
      },
      {
        question: 'هل تستخدمون ملفات تعريف الارتباط؟',
        answer: 'نستخدم ملفات تعريف الارتباط الأساسية فقط لتحسين تجربة الاستخدام، ولا نستخدمها لتتبع المستخدمين.'
      },
      {
        question: 'هل يمكنني استخدام الأدوات دون اتصال بالإنترنت؟',
        answer: 'معظم الأدوات تعمل محلياً في المتصفح، لكن تحتاج اتصال إنترنت لتحميل الصفحة أولاً. بعض الأدوات مثل محول العملات تحتاج اتصال مستمر للحصول على أسعار محدثة.'
      }
    ]
  },
  {
    title: 'التوافق التقني',
    icon: Smartphone,
    questions: [
      {
        question: 'هل الموقع متوافق مع الهواتف؟',
        answer: 'نعم، جميع الأدوات مصممة لتعمل بشكل مثالي على الهواتف الذكية والأجهزة اللوحية بالإضافة إلى أجهزة الكمبيوتر.'
      },
      {
        question: 'ما هي المتصفحات المدعومة؟',
        answer: 'الموقع يعمل على جميع المتصفحات الحديثة مثل Chrome، Firefox، Safari، Edge، وغيرها.'
      },
      {
        question: 'لماذا لا تعمل بعض الأدوات؟',
        answer: 'تأكد من أن متصفحك محدث وأن JavaScript مفعل. إذا استمرت المشكلة، جرب تحديث الصفحة أو استخدام متصفح آخر.'
      },
      {
        question: 'هل يمكنني استخدام الأدوات على أنظمة تشغيل مختلفة؟',
        answer: 'نعم، الموقع يعمل على جميع أنظمة التشغيل: Windows، Mac، Linux، iOS، Android.'
      }
    ]
  }
];

export default async function FAQPage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="الأسئلة الشائعة" 
        description="إجابات على الأسئلة الأكثر شيوعاً حول استخدام أدوات جامع الأدوات"
      />

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card className="text-center">
          <CardContent className="p-6">
            <Clock className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-bold text-lg mb-2">استجابة سريعة</h3>
            <p className="text-sm text-muted-foreground">نرد على استفساراتكم خلال 24 ساعة</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-6">
            <Zap className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-bold text-lg mb-2">سهولة الاستخدام</h3>
            <p className="text-sm text-muted-foreground">أدوات بسيطة ومفهومة للجميع</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-6">
            <Shield className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-bold text-lg mb-2">أمان وخصوصية</h3>
            <p className="text-sm text-muted-foreground">لا نحفظ أي بيانات شخصية</p>
          </CardContent>
        </Card>
      </div>

      {/* FAQ Categories */}
      <div className="space-y-8">
        {faqCategories.map((category, categoryIndex) => (
          <div key={categoryIndex}>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-primary/10 rounded-lg">
                <category.icon className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-headline font-bold">{category.title}</h2>
            </div>
            
            <div className="space-y-4">
              {category.questions.map((faq, questionIndex) => (
                <Card key={questionIndex}>
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Contact CTA */}
      <div className="mt-12 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-headline font-bold mb-4">
          لم تجد إجابة لسؤالك؟
        </h3>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          لا تتردد في التواصل معنا. فريقنا جاهز للإجابة على جميع استفساراتكم ومساعدتكم.
        </p>
        <a
          href="/p/contact"
          className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
        >
          <HelpCircle className="h-4 w-4" />
          اتصل بنا
        </a>
      </div>
    </div>
  );
}

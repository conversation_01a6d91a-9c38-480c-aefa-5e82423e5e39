
'use server';

/**
 * @fileOverview Generates relevant Arabic hashtags for a given keyword.
 *
 * - generateHashtags - A function that suggests hashtags.
 * - HashtagInput - The input type for the generateHashtags function.
 * - HashtagOutput - The return type for the generateHashtags function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const HashtagInputSchema = z.object({
  keyword: z.string().describe('الكلمة المفتاحية أو الموضوع الرئيسي للمحتوى.'),
});
export type HashtagInput = z.infer<typeof HashtagInputSchema>;

const HashtagOutputSchema = z.object({
  popular: z.array(z.string()).describe('قائمة من الهاشتاغات الشائعة وذات الوصول العالي.'),
  niche: z.array(z.string()).describe('قائمة من الهاشتاغات المتخصصة التي تستهدف جمهورًا دقيقًا.'),
  related: z.array(z.string()).describe('قائمة من الهاشتاغات ذات الصلة التي توفر سياقًا إضافيًا.'),
});
export type HashtagOutput = z.infer<typeof HashtagOutputSchema>;

export async function generateHashtags(input: HashtagInput): Promise<HashtagOutput> {
  return generateHashtagsFlow(input);
}

const generateHashtagsPrompt = ai.definePrompt({
  name: 'generateHashtagsPrompt',
  input: { schema: HashtagInputSchema },
  output: { schema: HashtagOutputSchema },
  prompt: `
    أنت خبير في التسويق عبر وسائل التواصل الاجتماعي متخصص في السوق العربي. مهمتك هي إنشاء قائمة من الهاشتاغات الفعالة لكلمة مفتاحية معينة.
    
    الكلمة المفتاحية: {{{keyword}}}
    
    يرجى تقديم ثلاث فئات من الهاشتاغات:
    1.  **شائعة (Popular):** 5 هاشتاغات ذات استخدام واسع جدًا لزيادة مدى الوصول.
    2.  **متخصصة (Niche):** 7 هاشتاغات تستهدف جمهورًا أكثر تحديدًا مهتمًا بالموضوع.
    3.  **ذات صلة (Related):** 5 هاشتاغات توفر سياقًا إضافيًا وتلامس مواضيع جانبية.
    
    تأكد من أن جميع الهاشتاغات باللغة العربية، بدون علامة #، وذات صلة بالمحتوى العربي.
  `,
});

const generateHashtagsFlow = ai.defineFlow(
  {
    name: 'generateHashtagsFlow',
    inputSchema: HashtagInputSchema,
    outputSchema: HashtagOutputSchema,
  },
  async (input) => {
    const { output } = await generateHashtagsPrompt(input);
    return output!;
  }
);

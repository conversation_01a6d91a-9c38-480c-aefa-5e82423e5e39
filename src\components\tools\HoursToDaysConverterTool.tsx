
'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const HOURS_IN_DAY = 24;

export function HoursToDaysConverterTool() {
  const [hours, setHours] = useState('24');
  const [days, setDays] = useState('1');

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setHours(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setDays((numValue / HOURS_IN_DAY).toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setDays('');
    }
  };

  const handleDaysChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDays(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setHours((numValue * HOURS_IN_DAY).toLocaleString('en-US', {maximumFractionDigits: 2, useGrouping: false}));
    } else {
      setHours('');
    }
  };
  
  const handleSwap = () => {
    const currentHours = hours;
    const currentDays = days;
    setHours(currentDays);
    setDays(currentHours);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من ساعات إلى أيام (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري للوقت.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 يوم = 24 ساعة
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="hours" className="text-sm font-medium mb-2 block">
              ساعات
            </label>
            <Input
              id="hours"
              type="number"
              value={hours}
              onChange={handleHoursChange}
              placeholder="أدخل الساعات"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="days" className="text-sm font-medium mb-2 block">
              أيام
            </label>
            <Input
              id="days"
              type="number"
              value={days}
              onChange={handleDaysChange}
              placeholder="أدخل الأيام"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Using string dates instead of Firebase Timestamp

export interface Article {
  id: string;
  title: string;
  slug: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  author: string;
  authorId: string;
  readTime: string;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  relatedTools?: string[];
  relatedArticles?: string[];
  imageUrl?: string;
  imageAlt?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  views: number;
  likes: number;
}

export interface ArticleCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  icon?: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateArticleData {
  title: string;
  slug: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  author: string;
  authorId: string;
  readTime: string;
  status: 'draft' | 'published';
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  relatedTools?: string[];
  relatedArticles?: string[];
  imageUrl?: string;
  imageAlt?: string;
}

export interface UpdateArticleData extends Partial<CreateArticleData> {
  id: string;
}

export interface ArticleFilters {
  category?: string;
  status?: 'draft' | 'published' | 'archived';
  featured?: boolean;
  author?: string;
  tags?: string[];
  search?: string;
}

export interface ArticleStats {
  totalArticles: number;
  publishedArticles: number;
  draftArticles: number;
  totalViews: number;
  totalLikes: number;
  categoriesCount: number;
  tagsCount: number;
}

export interface ArticleFormData {
  title: string;
  slug: string;
  description: string;
  content: string;
  category: string;
  tags: string;
  author: string;
  readTime: string;
  status: 'draft' | 'published';
  featured: boolean;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  relatedTools: string;
  relatedArticles: string;
  imageUrl: string;
  imageAlt: string;
}

// Helper types for forms
export interface ArticleFormData {
  title: string;
  slug: string;
  description: string;
  content: string;
  category: string;
  tags: string;
  author: string;
  readTime: string;
  status: 'draft' | 'published';
  featured: boolean;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  relatedTools: string;
  relatedArticles: string;
  imageUrl: string;
  imageAlt: string;
}

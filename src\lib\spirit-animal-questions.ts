
export type Answer = { text: string | { male: string; female: string }; points: number };
export type Question = { text: string | { male: string; female: string }; answers: Answer[] };

export const spiritAnimalQuestions: Question[] = [
  // Social Behavior & Environment
  {
    text: "أين تشعر بأكبر قدر من الراحة والطاقة؟",
    answers: [
      { text: "بمفردي في مكان هادئ، مثل الغابة أو الجبال.", points: 1 }, // Owl, Bear
      { text: "مع مجموعة صغيرة من الأصدقاء المقربين والعائلة.", points: 2 }, // Wolf, Dolphin
      { text: "في مكان مفتوح وواسع حيث يمكنني الرؤية لمسافات بعيدة.", points: 3 }, // Eagle
      { text: "في بيئة اجتماعية حيوية حيث أكون مركز الاهتمام.", points: 4 }, // Lion
    ],
  },
  {
    text: "عندما تواجه تحديًا، ما هو أسلوبك المفضل؟",
    answers: [
      { text: "المراقبة والتخطيط الدقيق قبل اتخاذ أي خطوة.", points: 1 }, // Owl, Fox
      { text: "العمل بشكل تعاوني مع فريقي لحل المشكلة معًا.", points: 2 }, // Wolf, Dolphin
      { text: "المواجهة المباشرة بقوة وثقة.", points: 4 }, // Lion, Bear
      { text: "استخدام السرعة والذكاء لإيجاد حل غير متوقع.", points: 3 }, // Fox
    ],
  },
  {
    text: "كيف تصف علاقتك بعائلتك أو 'مجموعتك'؟",
    answers: [
      { text: "أنا مخلص للغاية، ومستعد لفعل أي شيء لحمايتهم.", points: 2 }, // Wolf, Lion
      { text: "أنا أحبهم، ولكني أقدر استقلاليتي ومساحتي الخاصة.", points: 1 }, // Bear, Owl
      { text: "أنا القائد الطبيعي أو الشخص الذي يلجأون إليه طلبًا للمشورة.", points: 4 }, // Lion, Eagle
      { text: "علاقتنا مبنية على المرح والتواصل المستمر.", points: 3 }, // Dolphin
    ],
  },
  // Personality & Strengths
  {
    text: "ما هي أكبر نقاط قوتك؟",
    answers: [
      { text: "حكمتي وقدرتي على رؤية ما لا يراه الآخرون.", points: 1 }, // Owl
      { text: "شجاعتي وقدرتي على القيادة.", points: 4 }, // Lion
      { text: "ولائي وقدرتي على العمل ضمن فريق.", points: 2 }, // Wolf
      { text: "قوتي الجسدية وقدرتي على حماية نفسي ومن أحب.", points: 3 }, // Bear
    ],
  },
  {
    text: "كيف تتعامل مع المواقف الجديدة أو غير المألوفة؟",
    answers: [
      { text: "بحذر، أراقب وأجمع المعلومات أولاً.", points: 1 }, // Owl, Deer
      { text: "بفضول وذكاء، أحاول استكشاف الوضع واستغلاله لصالحي.", points: 3 }, // Fox
      { text: "بثقة، أعتبره تحديًا جديدًا لإثبات قدراتي.", points: 4 }, // Lion, Eagle
      { text: "بشكل اجتماعي ومرح، أحاول التفاعل واللعب.", points: 2 }, // Dolphin
    ],
  },
  {
    text: "ما هو أكثر ما يصفك عندما تكون في أفضل حالاتك؟",
    answers: [
      { text: "حكيم وهادئ", points: 1 }, // Owl
      { text: "قوي وواثق", points: 4 }, // Lion, Eagle
      { text: "لطيف ومتعاطف", points: 2 }, // Deer, Dolphin
      { text: "ذكي وماكر", points: 3 }, // Fox
    ],
  },
  // Challenges & Weaknesses
  {
    text: "عندما تشعر بالتهديد، ما هو رد فعلك الغريزي؟",
    answers: [
      { text: "الانسحاب إلى مكان آمن ومراقبة الوضع من بعيد.", points: 1 }, // Deer, Owl
      { text: "استخدام الحيلة والخداع للهروب أو قلب الموقف.", points: 3 }, // Fox
      { text: "التجمع مع حلفائي لمواجهة التهديد معًا.", points: 2 }, // Wolf
      { text: "المواجهة المباشرة والدفاع عن أرضي بقوة.", points: 4 }, // Bear, Lion
    ],
  },
  {
    text: "ما هو أكبر تحدٍ تواجهه في علاقاتك؟",
    answers: [
      { text: "الثقة بالآخرين بشكل كامل.", points: 3 }, // Fox
      { text: "الحاجة إلى الكثير من المساحة الشخصية.", points: 1 }, // Bear
      { text: "الميل إلى السيطرة أو القيادة.", points: 4 }, // Lion
      { text: "الاعتماد المفرط على مجموعتي أو عائلتي.", points: 2 }, // Wolf
    ],
  },
  // Aspirations & Lifestyle
  {
    text: "ما نوع المعرفة الذي تبحث عنه أكثر؟",
    answers: [
      { text: "الحكمة العميقة وفهم أسرار الحياة.", points: 1 }, // Owl
      { text: "استراتيجيات النجاح والتفوق.", points: 4 }, // Eagle, Lion
      { text: "فهم ديناميكيات المجموعة وكيفية التعاون.", points: 2 }, // Wolf
      { text: "مهارات البقاء والقدرة على التكيف.", points: 3 }, // Fox, Bear
    ],
  },
  {
    text: "ما هو شعارك في الحياة؟",
    answers: [
      { text: "القوة تكمن في المجموعة.", points: 2 }, // Wolf
      { text: "عش بحرية فوق الجميع.", points: 4 }, // Eagle
      { text: "اعرف نفسك، وعش بسلام.", points: 1 }, // Bear, Deer
      { text: "الذكاء يتفوق على القوة.", points: 3 }, // Fox
    ],
  },
];

export const getSpiritAnimalResult = (score: number) => {
    const results = {
      wolf: {
        animal: "الذئب",
        icon: "🐺",
        description: "أنت شخص مخلص للغاية، وتقدر الروابط الأسرية والاجتماعية أكثر من أي شيء آخر. قوتك الحقيقية تظهر عندما تعمل ضمن فريق. أنت ذكي، ومتواصل، وتتمتع بحدس قوي يجعلك قائدًا طبيعيًا وموثوقًا."
      },
      bear: {
        animal: "الدب",
        icon: "🐻",
        description: "أنت تمثل القوة الهادئة والشجاعة. لديك حضور قوي، ولكنك في نفس الوقت تحتاج إلى مساحتك الخاصة ووقتك للتأمل والراحة. أنت حامٍ لمن تحب، وتواجه تحديات الحياة بثبات وشجاعة."
      },
      owl: {
        animal: "البومة",
        icon: "🦉",
        description: "أنت روح حكيمة، ترى ما لا يراه الآخرون. تفضل الهدوء والمراقبة، وتستمد قوتك من المعرفة والتفكير العميق. لديك قدرة على رؤية الحقيقة وراء الأوهام، والناس يلجأون إليك طلبًا للنصيحة."
      },
      fox: {
        animal: "الثعلب",
        icon: "🦊",
        description: "أنت شخص ذكي، سريع البديهة، وقادر على التكيف مع أي موقف. تستخدم ذكاءك وحيلتك للتنقل في تحديات الحياة. قد تكون متحفظًا بعض الشيء، لكنك دائمًا ما تجد مخرجًا مبتكرًا من أصعب المواقف."
      },
      eagle: {
        animal: "النسر",
        icon: "🦅",
        description: "أنت تتمتع برؤية شاملة وقدرة على النظر إلى الصورة الكبيرة. أنت طموح، حر، ولا تخاف من التحليق عاليًا لتحقيق أهدافك. أنت قائد بالفطرة، وتلهم الآخرين بقوتك ورؤيتك الثاقبة."
      },
      lion: {
        animal: "الأسد",
        icon: "🦁",
        description: "أنت تجسيد للشجاعة والقيادة والكاريزما. لديك حضور مهيب وتستمتع بأن تكون مركز الاهتمام. أنت كريم وتحمي 'قطيعك' بشراسة، وتواجه الحياة بثقة ملكية."
      },
      deer: {
        animal: "الغزال",
        icon: "🦌",
        description: "أنت شخص لطيف، حساس، ومتعاطف. تتحرك في الحياة برشاقة وهدوء، وتتجنب الصراعات. قوتك تكمن في لطفك وقدرتك على الاستماع وفهم الآخرين بعمق. أنت تجلب السلام والسكينة لمن حولك."
      },

    };
  
    if (score <= 14) {
      return results.deer;
    }
    if (score <= 18) {
        return results.owl;
    }
    if (score <= 22) {
      return results.bear;
    }
    if (score <= 26) {
      return results.wolf;
    }
    if (score <= 30) {
      return results.fox;
    }
    if (score <= 35) {
      return results.lion;
    }
    return results.eagle;
};

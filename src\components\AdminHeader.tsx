'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  LogOut,
  Settings,
  FileText,
  BarChart3,
  Home,
  User,
  Shield
} from 'lucide-react';
import { useAuth } from '@/components/AuthProvider';

export function AdminHeader() {
  const { logout, user } = useAuth();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push('/admin/login');
  };

  return (
    <Card className="rounded-none border-b shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* الشعار والعنوان */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Settings className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">لوحة إدارة المقالات</h1>
            </div>
          </div>

          {/* القائمة الرئيسية */}
          <nav className="hidden md:flex items-center space-x-6 space-x-reverse">
            <Link
              href="/admin/articles"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <FileText className="h-4 w-4" />
              <span>المقالات</span>
            </Link>

            <Link
              href="/admin/articles/stats"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              <span>الإحصائيات</span>
            </Link>

            <Link
              href="/admin/credentials"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <User className="h-4 w-4" />
              <span>المستخدمين</span>
            </Link>

            <Link
              href="/admin/permissions-test"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <Shield className="h-4 w-4" />
              <span>اختبار الصلاحيات</span>
            </Link>

            <Link
              href="/admin/test-operations"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <Settings className="h-4 w-4" />
              <span>اختبار العمليات</span>
            </Link>

            <Link
              href="/"
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-blue-600 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span>الموقع الرئيسي</span>
            </Link>
          </nav>

          {/* معلومات المستخدم وتسجيل الخروج */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="hidden sm:flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span>مرحباً، {user?.full_name || user?.username || 'المدير'}</span>
            </div>

            <Link
              href="/admin/profile"
              className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
            >
              الملف الشخصي
            </Link>

            <Button
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 space-x-reverse"
            >
              <LogOut className="h-4 w-4" />
              <span>تسجيل الخروج</span>
            </Button>
          </div>
        </div>

        {/* القائمة المحمولة */}
        <nav className="md:hidden mt-4 flex items-center space-x-4 space-x-reverse border-t pt-4">
          <Link
            href="/admin/articles"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <FileText className="h-4 w-4" />
            <span>المقالات</span>
          </Link>

          <Link
            href="/admin/articles/stats"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <BarChart3 className="h-4 w-4" />
            <span>الإحصائيات</span>
          </Link>

          <Link
            href="/admin/credentials"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <User className="h-4 w-4" />
            <span>المستخدمين</span>
          </Link>

          <Link
            href="/admin/permissions-test"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <Shield className="h-4 w-4" />
            <span>اختبار الصلاحيات</span>
          </Link>

          <Link
            href="/admin/test-operations"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <Settings className="h-4 w-4" />
            <span>اختبار العمليات</span>
          </Link>

          <Link
            href="/"
            className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <Home className="h-4 w-4" />
            <span>الموقع</span>
          </Link>
        </nav>
      </CardContent>
    </Card>
  );
}

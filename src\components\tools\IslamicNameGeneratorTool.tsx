
'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Copy, RefreshCw, Search, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { islamicNames, IslamicName } from '@/lib/islamic-names';

function NameCard({ nameData, familyName }: { nameData: IslamicName, familyName: string }) {
    const { toast } = useToast();
    const fullName = `${nameData.name} ${familyName}`.trim();

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            toast({
                title: "تم النسخ!",
                description: `تم نسخ "${text}" إلى الحافظة.`,
            });
        });
    };

    return (
        <div className="border rounded-lg p-4 flex flex-col justify-between hover:border-primary/50 transition-colors bg-card">
            <div>
                <div className="flex justify-between items-start">
                    <h4 className="text-xl font-bold font-headline text-primary">{nameData.name}</h4>
                    <div className="flex gap-1">
                        {nameData.origin === 'quranic' && <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full border border-green-200">قرآني</span>}
                        {nameData.origin === 'arabic' && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full border border-blue-200">عربي</span>}
                    </div>
                </div>
                <p className="text-sm text-muted-foreground mt-1 mb-3">{nameData.meaning}</p>
            </div>
            <div className="mt-auto">
                {familyName && (
                    <div className="bg-muted p-2 rounded-md mb-3 text-center">
                        <p className="text-sm font-medium">{fullName}</p>
                    </div>
                )}
                <Button variant="outline" size="sm" className="w-full" onClick={() => copyToClipboard(nameData.name)}>
                    <Copy className="h-4 w-4 ml-2" />
                    نسخ الاسم
                </Button>
            </div>
        </div>
    );
}

export function IslamicNameGeneratorTool() {
    const [gender, setGender] = useState<'male' | 'female'>('male');
    const [searchTerm, setSearchTerm] = useState('');
    const [familyName, setFamilyName] = useState('');

    const filteredNames = useMemo(() => {
        return islamicNames.filter(name => {
            const genderMatch = name.gender === gender;
            const searchMatch = searchTerm === '' || name.name.includes(searchTerm) || name.meaning.includes(searchTerm);
            return genderMatch && searchMatch;
        });
    }, [gender, searchTerm]);

    const getRandomName = () => {
        const names = islamicNames.filter(name => name.gender === gender);
        const randomIndex = Math.floor(Math.random() * names.length);
        const randomName = names[randomIndex];
        setSearchTerm(randomName.name); // Set search term to show the random name
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle>مولد أسماء الأطفال الإسلامية</CardTitle>
                <CardDescription>اكتشف أسماء إسلامية جميلة وذات معنى لمولودك الجديد.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <Tabs value={gender} onValueChange={(value) => setGender(value as 'male' | 'female')} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="male">أسماء أولاد</TabsTrigger>
                        <TabsTrigger value="female">أسماء بنات</TabsTrigger>
                    </TabsList>
                </Tabs>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="relative">
                        <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="text"
                            placeholder="ابحث بالاسم أو بالمعنى..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pr-10"
                        />
                    </div>
                    <Button variant="outline" onClick={getRandomName}>
                        <Sparkles className="h-4 w-4 ml-2" />
                        اقترح لي اسمًا عشوائيًا
                    </Button>
                </div>
                
                 <div>
                    <label htmlFor="familyName" className="text-sm font-medium mb-2 block">
                        عرض الاسم مع اسم العائلة (اختياري)
                    </label>
                    <Input
                        id="familyName"
                        placeholder="أدخل اسم العائلة هنا..."
                        value={familyName}
                        onChange={(e) => setFamilyName(e.target.value)}
                    />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredNames.length > 0 ? (
                        filteredNames.map(nameData => (
                            <NameCard key={nameData.name} nameData={nameData} familyName={familyName} />
                        ))
                    ) : (
                        <p className="text-muted-foreground col-span-full text-center py-8">
                            لا توجد أسماء تطابق بحثك.
                        </p>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

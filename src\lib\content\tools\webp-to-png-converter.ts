
export default {
  seoDescription: `
    <div class="prose prose-lg max-w-none">
      <h1>أداة تحويل WebP إلى PNG: احصل على صور متوافقة عالميًا</h1>
      <p>صيغة WebP هي صيغة صور حديثة وفعالة للويب، ولكن قد تواجه أحيانًا بعض المشاكل في التوافق مع البرامج القديمة أو بعض المنصات التي لا تدعمها بشكل كامل. تقدم <strong>أداة تحويل WebP إلى PNG</strong> حلاً بسيطًا لتحويل صور WebP إلى صيغة PNG المدعومة عالميًا، والتي تحافظ على جودة الصورة وتدعم الشفافية.</p>
      
      <h2>لماذا قد تحتاج إلى التحويل من WebP إلى PNG؟</h2>
      <ul>
        <li><strong>التوافق:</strong> PNG هي صيغة مدعومة في كل مكان تقريبًا، من برامج تحرير الصور القديمة إلى جميع أنظمة التشغيل والمتصفحات.</li>
        <li><strong>الحفاظ على الجودة:</strong> تستخدم صيغة PNG ضغطًا غير فاقد للبيانات، مما يعني أنها الطريقة المثلى للحفاظ على أعلى جودة ممكنة للصورة الأصلية.</li>
        <li><strong>دعم الشفافية:</strong> إذا كانت صورة WebP الأصلية تحتوي على خلفية شفافة، فسيتم الحفاظ عليها تمامًا عند التحويل إلى PNG.</li>
      </ul>

      <h2>كيفية الاستخدام</h2>
      <ol>
        <li><strong>اختر صورة WebP:</strong> انقر لاختيار ملف صورة بصيغة WebP.</li>
        <li><strong>ابدأ التحويل:</strong> انقر على زر "تحويل إلى PNG".</li>
        <li><strong>حمّل صورتك الجديدة:</strong> سيتم تحويل الصورة على الفور وستكون جاهزة للتحميل بصيغة PNG.</li>
      </ol>

      <h3>معالجة آمنة في متصفحك</h3>
      <p>نحن نقدر خصوصيتك. تتم جميع عمليات التحويل <strong>محليًا في متصفحك</strong>. لا يتم رفع صورك إلى أي خادم، مما يضمن بقاءها آمنة على جهازك.</p>
    </div>
  `,
  faq: [
    {
      question: 'هل سأفقد جودة الصورة عند التحويل من WebP إلى PNG؟',
      answer: 'لا، على العكس. بما أن PNG هي صيغة ضغط غير فاقدة للبيانات، فستحافظ على جودة صورة WebP الأصلية بأقصى درجة ممكنة.'
    },
    {
      question: 'ماذا سيحدث لحجم الملف؟',
      answer: 'من المرجح أن يزيد حجم الملف بعد التحويل، لأن صيغة PNG عادة ما تكون أكبر حجمًا من صيغة WebP المضغوطة. الهدف من هذا التحويل هو زيادة التوافق وليس تقليل الحجم.'
    },
    {
      question: 'هل يمكنني استخدام هذه الأداة لتحويل الصور المتحركة بصيغة WebP؟',
      answer: 'حاليًا، تدعم الأداة تحويل صور WebP الثابتة فقط. سيتم تحويل الإطار الأول فقط من صورة WebP المتحركة.'
    }
  ]
};

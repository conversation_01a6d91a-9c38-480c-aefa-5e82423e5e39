
'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, Alert<PERSON><PERSON>gle, Shrink, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';

// Declare global types for PDF.js and PDF-lib
declare global {
  interface Window {
    pdfjsLib: any;
    PDFLib: any;
  }
}

export function PdfCompressorTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [originalSize, setOriginalSize] = useState<number>(0);
  const [compressedSize, setCompressedSize] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [libsLoaded, setLibsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progressText, setProgressText] = useState('');
  const [imageQuality, setImageQuality] = useState(75);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    const loadScripts = async () => {
      try {
        if (!window.pdfjsLib) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
          });
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
        if (!window.PDFLib) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        setLibsLoaded(true);
      } catch (err) {
        setError('فشل في تحميل المكتبات اللازمة. يرجى إعادة تحميل الصفحة.');
      }
    };
    loadScripts();
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setError('يرجى اختيار ملف PDF صالح.');
      return;
    }
    if (file.size > 100 * 1024 * 1024) {
      setError('حجم الملف كبير جدًا. يرجى اختيار ملف أصغر من 100 ميجابايت.');
      return;
    }

    setSelectedFile(file);
    setOriginalSize(file.size);
    setError(null);
    setCompressedSize(0);
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const compressPdf = async () => {
    if (!selectedFile || !libsLoaded) return;

    setIsProcessing(true);
    setError(null);
    setCompressedSize(0);

    try {
      const { PDFDocument } = window.PDFLib;
      const pdfjsLib = window.pdfjsLib;

      const existingPdfBytes = await selectedFile.arrayBuffer();
      const pdfToLoad = pdfjsLib.getDocument({ data: existingPdfBytes });
      const pdf = await pdfToLoad.promise;
      
      const newPdfDoc = await PDFDocument.create();
      const numPages = pdf.numPages;

      for (let i = 0; i < numPages; i++) {
        setProgressText(`معالجة الصفحة ${i + 1} من ${numPages}...`);
        const page = await pdf.getPage(i + 1);
        const viewport = page.getViewport({ scale: 1.5 }); // Scale for quality
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (!context) throw new Error('لا يمكن إنشاء سياق للرسم.');

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        await page.render({ canvasContext: context, viewport: viewport }).promise;

        const jpgUrl = canvas.toDataURL('image/jpeg', imageQuality / 100);
        const jpgImageBytes = await fetch(jpgUrl).then(res => res.arrayBuffer());
        const jpgImage = await newPdfDoc.embedJpg(jpgImageBytes);

        const newPage = newPdfDoc.addPage([viewport.width, viewport.height]);
        newPage.drawImage(jpgImage, {
          x: 0,
          y: 0,
          width: viewport.width,
          height: viewport.height,
        });
      }

      setProgressText('حفظ الملف المضغوط...');
      const pdfBytes = await newPdfDoc.save();
      
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      setCompressedSize(blob.size);
      saveAs(blob, `compressed_${selectedFile.name}`);
      toast({ title: 'تم ضغط الملف بنجاح!', description: 'بدأ تحميل الملف المضغوط.' });

    } catch (err) {
      console.error('PDF compression error:', err);
      setError('فشل ضغط الملف. قد يكون الملف معقداً أو محمياً.');
    } finally {
      setIsProcessing(false);
      setProgressText('');
    }
  };

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  const clearFile = () => {
    setSelectedFile(null);
    setError(null);
    setOriginalSize(0);
    setCompressedSize(0);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>ضاغط ملفات PDF</CardTitle>
        <CardDescription>قلل حجم ملفات PDF مع الحفاظ على أفضل جودة ممكنة. تتم المعالجة محلياً في متصفحك.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر ملف PDF للضغط</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الملف هنا أو انقر للاختيار</p>
                <Button>اختر ملف</Button>
              </div>
              <p className="text-sm text-gray-500">الحد الأقصى: 100 ميجابايت</p>
            </div>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".pdf" className="hidden" />
        </div>
        
        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {selectedFile && (
          <div className="space-y-4">
             <div className="flex items-center justify-between p-3 bg-muted rounded-lg border">
                  <div>
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">{formatBytes(selectedFile.size)}</p>
                  </div>
                  <Button onClick={clearFile} variant="ghost" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50"><Trash2 className="h-4 w-4" /></Button>
              </div>
            <div className="space-y-2">
                <Label htmlFor="quality-slider">جودة الضغط (للصور داخل الملف)</Label>
                <Slider
                  id="quality-slider"
                  defaultValue={[75]}
                  max={100}
                  step={5}
                  onValueChange={(value) => setImageQuality(value[0])}
                />
                 <div className="flex justify-between text-xs text-muted-foreground">
                    <span>جودة أقل (حجم أصغر)</span>
                    <span>جودة أعلى (حجم أكبر)</span>
                </div>
            </div>

            <Alert>
              <AlertDescription>
                تتم معالجة الملف بالكامل داخل متصفحك لضمان الخصوصية. هذا قد يستهلك موارد جهازك (المعالج والذاكرة)، لذا قد تكون العملية بطيئة للملفات الكبيرة جدًا أو على الأجهزة الضعيفة.
              </AlertDescription>
            </Alert>
            
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={compressPdf} disabled={isProcessing || !libsLoaded} className="flex-1">
                {isProcessing ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Shrink className="ml-2 h-4 w-4" />}
                {isProcessing ? progressText || 'جاري الضغط...' : 'ضغط الملف'}
              </Button>
            </div>
          </div>
        )}
        
        {compressedSize > 0 && (
          <div className="mt-6 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">اكتمل الضغط!</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">الحجم الأصلي</p>
                <p className="text-2xl font-bold">{formatBytes(originalSize)}</p>
              </div>
              <div className="p-4 bg-green-100 rounded-lg">
                <p className="text-sm text-green-700">الحجم الجديد</p>
                <p className="text-2xl font-bold text-green-800">{formatBytes(compressedSize)}</p>
              </div>
            </div>
            <p className="text-sm mt-2 text-muted-foreground">
              تم تقليل الحجم بنسبة {Math.round(((originalSize - compressedSize) / originalSize) * 100)}%
            </p>
          </div>
        )}

      </CardContent>
    </Card>
  );
}

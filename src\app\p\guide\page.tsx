import { Metadata } from 'next';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BookOpen, Play, Search, Calculator, Download, Share2 } from 'lucide-react';

export const metadata: Metadata = {
  title: 'دليل الاستخدام - جامع الأدوات',
  description: 'دليل شامل لاستخدام جميع أدوات جامع الأدوات. تعلم كيفية الاستفادة القصوى من الحاسبات والمحولات والأدوات المتوفرة.',
  keywords: ['دليل الاستخدام', 'كيفية الاستخدام', 'شرح الأدوات', 'مساعدة', 'جامع الأدوات'],
};

const steps = [
  {
    icon: Search,
    title: 'ابحث عن الأداة',
    description: 'استخدم شريط البحث أو تصفح الفئات للعثور على الأداة التي تحتاجها'
  },
  {
    icon: Play,
    title: 'ادخل البيانات',
    description: 'أدخل المعلومات المطلوبة في الحقول المخصصة بدقة'
  },
  {
    icon: Calculator,
    title: 'احصل على النتيجة',
    description: 'اضغط على زر الحساب للحصول على النتيجة فوراً'
  },
  {
    icon: Download,
    title: 'احفظ أو شارك',
    description: 'انسخ النتيجة أو اطبع الصفحة أو شاركها مع الآخرين'
  }
];

const toolCategories = [
  {
    title: 'الحاسبات المالية',
    tools: ['حاسبة الزكاة', 'حاسبة ضريبة القيمة المضافة', 'حاسبة الخصم', 'محول العملات'],
    tips: [
      'تأكد من إدخال المبالغ بالعملة الصحيحة',
      'راجع النتائج مع مختص مالي عند الحاجة',
      'احتفظ بسجل للحسابات المهمة'
    ]
  },
  {
    title: 'الحاسبات الرياضية',
    tools: ['الآلة الحاسبة', 'حاسبة النسبة المئوية', 'حاسبة الجذر التربيعي', 'حاسبة المتوسط'],
    tips: [
      'تحقق من صحة الأرقام المدخلة',
      'استخدم الأقواس للعمليات المعقدة',
      'راجع ترتيب العمليات الحسابية'
    ]
  },
  {
    title: 'المحولات',
    tools: ['محول الوحدات', 'محول التاريخ', 'محول درجة الحرارة'],
    tips: [
      'اختر الوحدة الصحيحة للتحويل',
      'تأكد من دقة القيم المدخلة',
      'راجع النتائج للتأكد من منطقيتها'
    ]
  },
  {
    title: 'أدوات النصوص',
    tools: ['عداد الكلمات', 'مكرر النص', 'عكس النص', 'مولد الهاشتاغ'],
    tips: [
      'انسخ النص بعناية لتجنب الأخطاء',
      'تحقق من التنسيق بعد المعالجة',
      'احفظ نسخة من النص الأصلي'
    ]
  }
];

const commonIssues = [
  {
    problem: 'الأداة لا تعمل',
    solutions: [
      'تأكد من تفعيل JavaScript في المتصفح',
      'حدث المتصفح إلى أحدث إصدار',
      'امسح ذاكرة التخزين المؤقت',
      'جرب متصفح آخر'
    ]
  },
  {
    problem: 'النتائج غير صحيحة',
    solutions: [
      'تحقق من صحة البيانات المدخلة',
      'تأكد من اختيار الوحدات الصحيحة',
      'راجع تعليمات الأداة',
      'جرب إدخال البيانات مرة أخرى'
    ]
  },
  {
    problem: 'لا يمكنني العثور على أداة معينة',
    solutions: [
      'استخدم شريط البحث',
      'تصفح الفئات المختلفة',
      'تحقق من الصفحة الرئيسية',
      'اتصل بنا لطلب إضافة الأداة'
    ]
  }
];

export default async function GuidePage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="دليل الاستخدام" 
        description="تعلم كيفية استخدام جميع أدوات جامع الأدوات بفعالية وسهولة"
      />

      {/* Getting Started */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          كيفية البدء
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <Card key={index} className="text-center h-full">
              <CardContent className="p-6">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <step.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <h3 className="font-bold text-lg mb-3">{step.title}</h3>
                <p className="text-sm text-muted-foreground">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tool Categories Guide */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          دليل الفئات
        </h2>
        <div className="space-y-6">
          {toolCategories.map((category, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-xl">{category.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <h4 className="font-bold mb-2">الأدوات المتوفرة:</h4>
                  <div className="flex flex-wrap gap-2">
                    {category.tools.map((tool, toolIndex) => (
                      <span
                        key={toolIndex}
                        className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                      >
                        {tool}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-bold mb-2">نصائح للاستخدام:</h4>
                  <ul className="list-disc mr-6 space-y-1">
                    {category.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="text-muted-foreground">{tip}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Common Issues */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          حل المشاكل الشائعة
        </h2>
        <div className="space-y-4">
          {commonIssues.map((issue, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-lg text-red-600">{issue.problem}</CardTitle>
              </CardHeader>
              <CardContent>
                <h4 className="font-bold mb-2">الحلول المقترحة:</h4>
                <ol className="list-decimal mr-6 space-y-1">
                  {issue.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex} className="text-muted-foreground">{solution}</li>
                  ))}
                </ol>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tips and Tricks */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          نصائح وحيل
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                نصائح عامة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">احفظ الموقع في المفضلة للوصول السريع</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">استخدم اختصارات لوحة المفاتيح للسرعة</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">تحقق من النتائج قبل اتخاذ قرارات مهمة</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">شارك الأدوات المفيدة مع الأصدقاء</span>
                </li>
              </ul>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Share2 className="h-5 w-5" />
                المشاركة والحفظ
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">انسخ الرابط لمشاركة أداة معينة</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">اطبع الصفحة لحفظ النتائج</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">التقط لقطة شاشة للنتائج المهمة</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">استخدم زر المشاركة في الأدوات</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-headline font-bold mb-4">
          تحتاج مساعدة إضافية؟
        </h3>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          إذا لم تجد الإجابة التي تبحث عنها في هذا الدليل، لا تتردد في التواصل معنا. فريقنا جاهز لمساعدتك.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/p/contact"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
          >
            اتصل بنا
          </a>
          <a
            href="/p/faq"
            className="inline-flex items-center gap-2 border border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary/10 transition-colors"
          >
            الأسئلة الشائعة
          </a>
        </div>
      </div>
    </div>
  );
}

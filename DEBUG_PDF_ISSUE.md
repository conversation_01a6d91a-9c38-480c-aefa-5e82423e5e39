# تشخيص مشكلة PDF - مولد الفواتير ZATCA

## المشاكل المُبلغ عنها:

1. **الأزرار تختفي** عند الضغط على "تحميل PDF"
2. **PDF فارغ** لا يحتوي على محتوى الفاتورة

## خطوات التشخيص:

### 1. إنشاء فاتورة تجريبية
1. افتح: `http://localhost:9003/tools/zatca-invoice-generator`
2. أدخل البيانات التالية:
   - **اسم البائع:** شركة الاختبار
   - **الرقم الضريبي:** 123456789012345
   - **رقم الفاتورة:** TEST-001
   - **تاريخ الفاتورة:** 2024-01-15

### 2. إضافة عناصر للفاتورة
أضف عنصر واحد على الأقل:
- **الوصف:** خدمة تجريبية
- **الكمية:** 1
- **السعر:** 100

### 3. معاينة الفاتورة
اضغط على "معاينة الفاتورة" وتأكد من ظهور:
- ✅ بيانات البائع
- ✅ بيانات الفاتورة
- ✅ العناصر المُضافة
- ✅ QR Code
- ✅ المجاميع

### 4. فتح Developer Tools
1. اضغط F12 أو انقر بالزر الأيمن واختر "Inspect"
2. اذهب إلى تبويب "Console"

### 5. اختبار تحميل PDF
1. اضغط على زر "تحميل PDF"
2. راقب ما يحدث:
   - هل تختفي الأزرار؟
   - هل يظهر "جاري إنشاء PDF..."؟
   - ما الرسائل في Console؟

## المعلومات التشخيصية المتوقعة في Console:

```javascript
Invoice element info: {
  width: [رقم],
  height: [رقم],
  offsetWidth: [رقم],
  offsetHeight: [رقم],
  itemsCount: [عدد العناصر],
  hasContent: true,
  isVisible: true,
  textContent: "شركة الاختبار..."
}

Canvas info: {
  width: [رقم],
  height: [رقم],
  isEmpty: false,
  dataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

## التحقق من المشاكل:

### إذا اختفت الأزرار:
- ✅ **طبيعي:** الأزرار لها class `print:hidden` ولا يجب أن تختفي
- ❌ **مشكلة:** إذا اختفت فهناك خطأ في الكود

### إذا كان PDF فارغ:
تحقق من Console:
- **Canvas width/height = 0:** مشكلة في html2canvas
- **Invoice element not found:** مشكلة في العثور على الفاتورة
- **hasContent = false:** الفاتورة فارغة
- **isVisible = false:** الفاتورة مخفية

## الحلول المحتملة:

### إذا كانت الأزرار تختفي:
```javascript
// المشكلة: الكود يخفي العناصر الخاطئة
// الحل: التأكد من أن الأزرار خارج invoice-container
```

### إذا كان PDF فارغ:
```javascript
// المشكلة: html2canvas لا يرى المحتوى
// الحل: إضافة انتظار أو تحسين إعدادات html2canvas
```

## الكود المُحدث:

تم إضافة التحسينات التالية:
1. **معلومات تشخيصية:** لفهم المشكلة
2. **انتظار المحتوى:** 500ms قبل إنشاء Canvas
3. **تحسين html2canvas:** إعدادات أفضل للعرض
4. **فحص Canvas فارغ:** رسالة خطأ واضحة

## النتائج المتوقعة بعد الإصلاح:

- ✅ الأزرار لا تختفي
- ✅ PDF يحتوي على محتوى الفاتورة
- ✅ جودة عالية للنصوص العربية
- ✅ QR Code واضح في PDF
- ✅ رسائل خطأ واضحة إذا حدثت مشاكل

## تعليمات الاختبار:

1. **اتبع الخطوات أعلاه**
2. **انسخ المعلومات من Console**
3. **أرسل النتائج للمطور**
4. **اذكر بالتفصيل ما يحدث**

هذا سيساعد في تحديد المشكلة الدقيقة وإصلاحها.

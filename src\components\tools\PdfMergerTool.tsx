
'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Merge, Loader2, X, GripVertical } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';

// Declare global types for PDF-lib and PDF.js
declare global {
  interface Window {
    PDFLib: any;
    pdfjsLib: any;
  }
}

interface PdfFile {
  file: File;
  id: string;
  name: string;
  size: string;
}

export function PdfMergerTool() {
  const [selectedFiles, setSelectedFiles] = useState<PdfFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [libsLoaded, setLibsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressText, setProgressText] = useState('');
  const [finalPdfBlob, setFinalPdfBlob] = useState<Blob | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [compressOutput, setCompressOutput] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    const loadScripts = async () => {
      try {
        if (!window.PDFLib) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js';
            script.onload = () => resolve();
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        if (!window.pdfjsLib) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = () => {
              if (window.pdfjsLib) {
                  window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
              }
              resolve();
            };
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }
        setLibsLoaded(true);
      } catch (err) {
        console.error(err);
        setError('فشل في تحميل المكتبات اللازمة. يرجى إعادة تحميل الصفحة.');
      }
    };
    loadScripts();
  }, []);
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const generateId = (): string => Math.random().toString(36).substr(2, 9);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');
    if (pdfFiles.length === 0) {
      setError('يرجى اختيار ملفات PDF صالحة.');
      return;
    }

    const oversizedFiles = pdfFiles.filter(file => file.size > 100 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      setError('بعض الملفات كبيرة جداً. يرجى اختيار ملفات أصغر من 100 ميجابايت.');
      return;
    }

    const newFiles: PdfFile[] = pdfFiles.map(file => ({
      file, id: generateId(), name: file.name, size: formatFileSize(file.size)
    }));

    const existingNames = new Set(selectedFiles.map(f => f.name));
    const uniqueFiles = newFiles.filter(f => !existingNames.has(f.name));

    if (uniqueFiles.length === 0) {
      setError('جميع الملفات المختارة موجودة بالفعل.');
      return;
    }

    setSelectedFiles(prev => [...prev, ...uniqueFiles]);
    setError(null);
    setSuccess(null);
    setFinalPdfBlob(null);

    toast({ title: 'تم إضافة الملفات', description: `تم إضافة ${uniqueFiles.length} ملف PDF جديد.` });

    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  const removeFile = (id: string) => setSelectedFiles(prev => prev.filter(file => file.id !== id));
  const clearAllFiles = () => {
    setSelectedFiles([]);
    setError(null);
    setSuccess(null);
    setFinalPdfBlob(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const moveFile = (fromIndex: number, toIndex: number) => {
    const newFiles = [...selectedFiles];
    const [movedFile] = newFiles.splice(fromIndex, 1);
    newFiles.splice(toIndex, 0, movedFile);
    setSelectedFiles(newFiles);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => setDraggedIndex(index);
  const handleDragEnd = () => setDraggedIndex(null);
  const handleFileItemDragOver = (e: React.DragEvent) => e.preventDefault();
  const handleFileItemDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      moveFile(draggedIndex, dropIndex);
    }
    setDraggedIndex(null);
  };
  
  const processFiles = async () => {
    if (selectedFiles.length < 2 || !libsLoaded) {
      setError('يجب اختيار ملفين على الأقل للدمج.');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Step 1: Merge PDFs
      setProgressText('الخطوة 1: دمج الملفات...');
      const { PDFDocument } = window.PDFLib;
      const mergedPdf = await PDFDocument.create();

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const arrayBuffer = await file.file.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer, { ignoreEncryption: true });
        const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        pages.forEach((page: any) => mergedPdf.addPage(page));
        setProgress(compressOutput ? (i / selectedFiles.length) * 50 : (i / selectedFiles.length) * 100);
      }
      
      let finalPdfBytes = await mergedPdf.save();
      
      if (compressOutput) {
        // Step 2: Compress the merged PDF
        setProgressText('الخطوة 2: ضغط الملف المدموج...');
        const { pdfjsLib } = window;
        const pdfToLoad = pdfjsLib.getDocument({ data: finalPdfBytes });
        const pdfToCompress = await pdfToLoad.promise;
        
        const compressedPdfDoc = await PDFDocument.create();
        const numPages = pdfToCompress.numPages;

        for (let i = 0; i < numPages; i++) {
          const page = await pdfToCompress.getPage(i + 1);
          const viewport = page.getViewport({ scale: 1.5 });
          const canvas = document.createElement('canvas');
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          const context = canvas.getContext('2d');
          if (!context) throw new Error('لا يمكن إنشاء سياق للرسم.');

          await page.render({ canvasContext: context, viewport }).promise;
          const jpgUrl = canvas.toDataURL('image/jpeg', 0.90); // 90% quality
          const jpgImageBytes = await fetch(jpgUrl).then(res => res.arrayBuffer());
          const jpgImage = await compressedPdfDoc.embedJpg(jpgImageBytes);

          const newPage = compressedPdfDoc.addPage([viewport.width, viewport.height]);
          newPage.drawImage(jpgImage, { x: 0, y: 0, width: viewport.width, height: viewport.height });
          setProgress(50 + ((i + 1) / numPages) * 50);
        }
        
        finalPdfBytes = await compressedPdfDoc.save();
      }

      setProgressText('حفظ الملف النهائي...');
      const blob = new Blob([finalPdfBytes], { type: 'application/pdf' });
      setFinalPdfBlob(blob);
      
      setSuccess(`تم ${compressOutput ? 'دمج وضغط' : 'دمج'} ${selectedFiles.length} ملف بنجاح!`);
      
    } catch (err) {
      console.error('PDF processing error:', err);
      setError('فشل في معالجة الملفات. تأكد من أن جميع الملفات صالحة وغير محمية بكلمة مرور.');
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setProgressText('');
    }
  };

  const downloadMergedPDF = () => {
    if (finalPdfBlob) {
        saveAs(finalPdfBlob, `merged_${Date.now()}.pdf`);
        toast({ title: 'تم التحميل', description: 'تم تحميل الملف المدموج.' });
    }
  };

  const handleDragOver = (e: React.DragEvent) => e.preventDefault();
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const event = { target: { files: e.dataTransfer.files } } as any;
    handleFileChange(event);
  };
  
  if (!libsLoaded) {
    return (
      <Card className="w-full max-w-4xl mx-auto"><CardContent className="p-8 flex items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin mr-2" /> جاري تحميل المكتبات...
      </CardContent></Card>
    );
  }
  
  return (
    <Card className="w-full max-w-4xl mx-auto" onDragOver={handleDragOver} onDrop={handleDrop}>
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2 text-2xl">
            <Merge className="h-8 w-8 text-primary" />
            دمج ملفات PDF
          </CardTitle>
          <CardDescription className="text-center">
            ادمج ملفاتك في ملف واحد، مع خيار ضغطه تلقائيًا للحصول على أصغر حجم ممكن.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر ملفات PDF للدمج</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الملفات هنا أو انقر للاختيار</p>
                <Button>اختر ملفات</Button>
              </div>
            </div>
            <input ref={fileInputRef} type="file" multiple accept=".pdf" onChange={handleFileChange} className="hidden" />
          </div>

          {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}
          {success && <Alert className="border-green-200 bg-green-50"><Download className="h-4 w-4 text-green-600" /><AlertDescription className="text-green-800">{success}</AlertDescription></Alert>}
          
          {isProcessing && (
            <div className="p-4 bg-muted rounded-lg space-y-2">
              <div className="flex justify-between text-sm"><span>{progressText}</span><span>{Math.round(progress)}%</span></div>
              <div className="w-full bg-gray-200 rounded-full h-2.5"><div className="bg-primary h-2.5 rounded-full" style={{ width: `${progress}%` }}></div></div>
            </div>
          )}

          {selectedFiles.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">الملفات المختارة ({selectedFiles.length})</h3>
                <Button variant="outline" size="sm" onClick={clearAllFiles} className="text-red-600 hover:text-red-700"><Trash2 className="h-4 w-4 ml-2" /> مسح الكل</Button>
              </div>
              <div className="space-y-3 max-h-96 overflow-y-auto p-2 border rounded-md">
                {selectedFiles.map((file, index) => (
                  <div key={file.id} draggable onDragStart={e => handleDragStart(e, index)} onDragEnd={handleDragEnd} onDragOver={handleFileItemDragOver} onDrop={e => handleFileItemDrop(e, index)} className={`flex items-center justify-between p-3 rounded-lg border transition-all ${draggedIndex === index ? 'bg-blue-100 border-blue-300 shadow-lg' : 'bg-gray-50 hover:bg-gray-100'}`}>
                    <div className="flex items-center gap-3"><GripVertical className="h-5 w-5 text-gray-400 cursor-move" /><div className="p-2 bg-red-100 rounded"><FileText className="h-5 w-5 text-red-600" /></div><div><p className="font-medium text-sm">{file.name}</p><p className="text-xs text-gray-500">{file.size}</p></div></div>
                    <Button variant="ghost" size="sm" onClick={() => removeFile(file.id)} className="text-red-600 hover:text-red-700 hover:bg-red-50"><X className="h-4 w-4" /></Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedFiles.length > 0 && (
            <div>
              <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm mb-4">
                <div className="space-y-0.5">
                  <Label className="text-base font-semibold">ضغط الملف النهائي</Label>
                  <p className="text-sm text-muted-foreground">تقليل حجم الملف النهائي (قد يؤثر قليلاً على الجودة).</p>
                </div>
                <Switch checked={compressOutput} onCheckedChange={setCompressOutput} />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button onClick={processFiles} disabled={selectedFiles.length < 2 || isProcessing} className="flex-1 bg-green-600 hover:bg-green-700" size="lg">
                  {isProcessing ? <><Loader2 className="h-5 w-5 ml-2 animate-spin" /> جاري المعالجة...</> : <><Merge className="h-5 w-5 ml-2" /> دمج الملفات</>}
                </Button>
                {finalPdfBlob && <Button onClick={downloadMergedPDF} className="flex-1" size="lg"><Download className="h-5 w-5 ml-2" /> تحميل الملف النهائي</Button>}
              </div>
              {selectedFiles.length < 2 && <p className="text-sm text-center text-muted-foreground mt-2">يجب اختيار ملفين على الأقل للدمج.</p>}
            </div>
          )}
        </CardContent>
    </Card>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from '@/components/PageHeader';
import { 
  Database, 
  Users, 
  Shield, 
  Calendar,
  Mail,
  User,
  Key,
  Info
} from 'lucide-react';
import { AdminUser, getAllUsers } from '@/lib/auth-supabase';

export default function UsersInfoPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      const usersData = await getAllUsers();
      setUsers(usersData);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="معلومات المستخدمين" 
          description="عرض بيانات المستخدمين المحفوظة في قاعدة البيانات"
        />
        <div className="animate-pulse">
          <Card>
            <CardContent className="p-6">
              <div className="h-40 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="معلومات المستخدمين" 
        description="عرض بيانات المستخدمين المحفوظة في قاعدة البيانات"
      />

      {/* معلومات النظام */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Database className="h-5 w-5" />
            <span>معلومات النظام</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3 space-x-reverse p-3 bg-green-50 rounded-lg">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium text-green-900">نوع المصادقة</p>
                <p className="text-sm text-green-700">قاعدة بيانات Supabase</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 space-x-reverse p-3 bg-blue-50 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium text-blue-900">إجمالي المستخدمين</p>
                <p className="text-sm text-blue-700">{users.length} مستخدم</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* بيانات تسجيل الدخول */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Key className="h-5 w-5" />
            <span>بيانات تسجيل الدخول</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-center space-x-2 space-x-reverse mb-2">
              <Info className="h-4 w-4 text-yellow-600" />
              <p className="text-sm font-medium text-yellow-800">
                ملاحظة: كلمات المرور محفوظة مشفرة في قاعدة البيانات
              </p>
            </div>
          </div>

          {/* جدول بيانات تسجيل الدخول */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">جدول بيانات تسجيل الدخول</h3>
            <div className="overflow-hidden rounded-lg border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم المستخدم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      كلمة المرور
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الاسم الكامل
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الدور
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {user.username}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {user.username === 'admin' ? 'admin123' :
                           user.username === 'manager' ? 'manager123' :
                           user.username === 'editor' ? 'editor123' : 'غير محددة'}
                        </code>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {user.full_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        <Badge variant="outline" className="text-xs">
                          {user.role}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        <Badge
                          variant={user.is_active ? "default" : "destructive"}
                          className="text-xs"
                        >
                          {user.is_active ? 'نشط' : 'معطل'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="grid gap-4">
            {users.map((user) => (
              <div 
                key={user.id} 
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 rounded-full bg-blue-100">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{user.full_name}</h3>
                      <p className="text-sm text-gray-600">@{user.username}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Badge 
                      variant={user.is_active ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {user.is_active ? 'نشط' : 'معطل'}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {user.role}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <User className="h-3 w-3" />
                      <span>اسم المستخدم:</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {user.username}
                      </code>
                    </div>
                    
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <Mail className="h-3 w-3" />
                      <span>البريد الإلكتروني:</span>
                      <span className="text-blue-600">{user.email}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <Calendar className="h-3 w-3" />
                      <span>تاريخ الإنشاء:</span>
                      <span>{formatDate(user.created_at)}</span>
                    </div>
                    
                    {user.last_login && (
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                        <Shield className="h-3 w-3" />
                        <span>آخر دخول:</span>
                        <span>{formatDate(user.last_login)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* معلومات كلمة المرور للاختبار */}
                <div className="mt-3 pt-3 border-t">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-600 mb-1">
                      <strong>كلمة المرور للاختبار:</strong>
                    </p>
                    <code className="text-xs bg-white px-2 py-1 rounded border">
                      {user.username === 'admin' ? 'admin123' : 
                       user.username === 'manager' ? 'manager123' : 
                       user.username === 'editor' ? 'editor123' : 'غير محددة'}
                    </code>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

const content = {
  seoDescription: `
      <h2>حاسبة تحويل الدقائق إلى ساعات: أداة دقيقة لإدارة الوقت</h2>
      <p>سواء كنت تخطط لمشروع، أو تحسب مدة رحلة، أو ببساطة تريد فهم الفترات الزمنية بشكل أفضل، فإن تحويل الدقائق إلى ساعات هو عملية حسابية أساسية. تقدم <strong>أداة تحويل الدقائق إلى ساعات</strong> حلاً فوريًا ودقيقًا لهذه المهمة، مما يسهل عليك التعامل مع الوقت بكفاءة.</p>

      <h3>كيفية استخدام محول الدقائق إلى ساعات</h3>
      <p>صُممت أداتنا لتكون سهلة للغاية:</p>
      <ol>
        <li><strong>أدخل القيمة:</strong> اكتب عدد الدقائق الذي تريد تحويله في حقل "دقائق"، أو عدد الساعات في حقل "ساعات".</li>
        <li><strong>شاهد النتيجة الفورية:</strong> بمجرد إدخال القيمة، سيتم حساب القيمة المعادلة وعرضها في الحقل الآخر تلقائيًا.</li>
      </ol>
      <p>تعتمد الأداة على المعادلة البسيطة: <strong>1 ساعة = 60 دقيقة</strong>.</p>

      <h3>من يستفيد من هذه الأداة؟</h3>
      <ul>
        <li><strong>مديرو المشاريع:</strong> لتقدير الوقت اللازم للمهام وتوزيعها.</li>
        <li><strong>الطلاب:</strong> لحساب وقت المحاضرات أو مدة المذاكرة.</li>
        <li><strong>المسافرون:</strong> لتقدير مدة الرحلات الجوية أو البرية.</li>
        <li><strong>أي شخص:</strong> يحتاج إلى حساب سريع للوقت في حياته اليومية.</li>
      </ul>
    `,
  faq: [
    { question: 'كم دقيقة في الساعة الواحدة؟', answer: 'الساعة الواحدة تحتوي على 60 دقيقة.' },
    { question: 'كيف يمكنني تحويل الساعات والدقائق إلى ساعات فقط؟', answer: 'إذا كان لديك 1 ساعة و 30 دقيقة، يمكنك تحويل الدقائق إلى ساعات (30 / 60 = 0.5)، ثم إضافتها إلى الساعات الكاملة. النتيجة هي 1.5 ساعة. أداتنا تقوم بذلك تلقائيًا.' },
    { question: 'هل يمكنني استخدام كسور عشرية؟', answer: 'نعم، يمكنك إدخال أرقام عشرية في كلا الحقلين (مثل 1.5 ساعة) وستقوم الأداة بالحساب بشكل صحيح.' }
  ]
};
export default content;

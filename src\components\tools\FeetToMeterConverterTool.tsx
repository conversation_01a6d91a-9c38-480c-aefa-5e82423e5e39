
'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const FEET_TO_METER_FACTOR = 0.3048;

export function FeetToMeterConverterTool() {
  const [feet, setFeet] = useState('1');
  const [meters, setMeters] = useState(FEET_TO_METER_FACTOR.toString());

  const handleFeetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFeet(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setMeters((numValue * FEET_TO_METER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setMeters('');
    }
  };

  const handleMetersChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMeters(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setFeet((numValue / FEET_TO_METER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setFeet('');
    }
  };

  const handleSwap = () => {
    const currentFeet = feet;
    const currentMeters = meters;
    setFeet(currentMeters);
    setMeters(currentFeet);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من قدم إلى متر (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 قدم = 0.3048 متر
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="feet" className="text-sm font-medium mb-2 block">
              قدم (Feet)
            </label>
            <Input
              id="feet"
              type="number"
              value={feet}
              onChange={handleFeetChange}
              placeholder="أدخل المسافة بالقدم"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="meters" className="text-sm font-medium mb-2 block">
              متر (Meter)
            </label>
            <Input
              id="meters"
              type="number"
              value={meters}
              onChange={handleMetersChange}
              placeholder="أدخل المسافة بالمتر"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

# مولد الفواتير الإلكترونية - ZATCA

## نظرة عامة

تم إضافة أداة مولد الفواتير الإلكترونية المتوافقة مع متطلبات هيئة الزكاة والضريبة والجمارك (ZATCA) بنجاح إلى الموقع. هذه الأداة توفر حلاً شاملاً لإنشاء فواتير ضريبية احترافية ومتوافقة مع اللوائح السعودية.

## المميزات الرئيسية

### 1. التوافق الكامل مع ZATCA
- ✅ يلبي جميع متطلبات هيئة الزكاة والضريبة والجمارك
- ✅ دعم الفواتير الضريبية والفواتير الضريبية المبسطة
- ✅ إنشاء رمز QR متوافق بتنسيق TLV Base64

### 2. واجهة مستخدم متقدمة
- 🎨 تصميم احترافي وسهل الاستخدام
- 📱 متجاوب مع جميع أحجام الشاشات
- 🌙 دعم الطباعة مع تصميم محسن للطباعة
- 🔄 معاينة فورية للفاتورة

### 3. إدارة البيانات الذكية
- 💾 حفظ تلقائي لبيانات البائع في المتصفح
- 📋 نظام القوالب لحفظ وتحميل الفواتير المتكررة
- 🔢 إنشاء تلقائي لأرقام الفواتير
- ✏️ تحقق من صحة البيانات في الوقت الفعلي

### 4. حسابات دقيقة
- 🧮 حساب تلقائي لضريبة القيمة المضافة (15%)
- 📊 عرض المجاميع في الوقت الفعلي
- 💰 دعم عدة منتجات وخدمات
- 🔍 تحقق من صحة الأرقام الضريبية

## الملفات المضافة

### 1. مكون الأداة الرئيسي
```
src/components/tools/ZatcaInvoiceGeneratorTool.tsx
```
- مكون React متكامل للأداة
- واجهة مستخدم تفاعلية
- منطق الأعمال والحسابات
- إنشاء رمز QR متوافق مع ZATCA

### 2. تسجيل الأداة
```
src/lib/tools.ts (تم التحديث)
src/lib/tool-registry.ts (تم التحديث)
```
- إضافة الأداة إلى قائمة الحاسبات المالية
- تسجيل المكون في نظام الأدوات

### 3. محتوى SEO
```
src/lib/content/tools/zatca-invoice-generator.ts
```
- وصف شامل للأداة
- أسئلة شائعة (FAQ)
- محتوى محسن لمحركات البحث

## كيفية الاستخدام

### 1. إدخال بيانات البائع
- اسم البائع (مطلوب)
- الرقم الضريبي (15 رقم - مطلوب)
- رقم السجل التجاري (اختياري)
- العنوان (مطلوب)

### 2. إدخال بيانات المشتري
- اسم المشتري (اختياري للفواتير المبسطة)
- الرقم الضريبي للمشتري (اختياري)
- رقم السجل التجاري للمشتري (اختياري)

### 3. تفاصيل الفاتورة
- رقم الفاتورة (يتم إنشاؤه تلقائياً)
- تاريخ الفاتورة (اليوم افتراضياً)

### 4. إضافة المنتجات/الخدمات
- وصف المنتج أو الخدمة
- الكمية
- سعر الوحدة (بدون ضريبة)

### 5. إنشاء ومعاينة الفاتورة
- مراجعة البيانات
- معاينة الفاتورة
- طباعة أو تحميل PDF

## المميزات التقنية

### 1. رمز QR متوافق مع ZATCA ✅ تم الإصلاح
```typescript
// تنسيق TLV (Tag-Length-Value) مع QR Code حقيقي قابل للقراءة
const tlvData =
  createTLV(1, sellerName) +
  createTLV(2, sellerVAT) +
  createTLV(3, dateTime) +
  createTLV(4, total.toFixed(2)) +
  createTLV(5, vatAmount.toFixed(2));

// إنشاء QR Code حقيقي باستخدام QR Server API
const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(base64Data)}&size=128x128&format=png&margin=0`;
```

**الإصلاحات المُطبقة:**
- ✅ استبدال QR Code المزيف بـ QR Code حقيقي قابل للقراءة
- ✅ استخدام QR Server API لإنشاء رموز QR صحيحة
- ✅ إضافة أداة فك تشفير QR Code منفصلة للاختبار
- ✅ تحسين عرض البيانات وإضافة معلومات تشخيصية
- ✅ إصلاح مشكلة تحميل PDF - الآن ينتج ملف PDF حقيقي

### 2. التحقق من صحة البيانات
- تحقق من طول الرقم الضريبي (15 رقم)
- تحقق من وجود البيانات المطلوبة
- تحقق من صحة الأسعار والكميات

### 3. الحفظ المحلي
- حفظ بيانات البائع تلقائياً
- نظام القوالب للفواتير المتكررة
- عدم إرسال البيانات إلى خوادم خارجية

### 4. التصميم المتجاوب
- يعمل على جميع الأجهزة
- تصميم محسن للطباعة
- واجهة عربية كاملة

## الرابط المباشر
```
/tools/zatca-invoice-generator
```

## ملاحظات مهمة

### الأمان والخصوصية
- جميع البيانات تتم معالجتها محلياً في المتصفح
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- البيانات محفوظة في localStorage للمتصفح فقط

### التوافق القانوني
- متوافق مع لوائح هيئة الزكاة والضريبة والجمارك
- يدعم الفواتير الضريبية والمبسطة
- رمز QR بالتنسيق المطلوب قانونياً

### الدعم التقني
- تعمل على جميع المتصفحات الحديثة
- لا تتطلب تثبيت أي برامج إضافية
- واجهة سهلة ومفهومة

## التحديثات المستقبلية المقترحة

1. **دعم العملات المتعددة**: إضافة دعم لعملات أخرى غير الريال السعودي
2. **قوالب متقدمة**: المزيد من خيارات التخصيص للفواتير
3. **تصدير متقدم**: دعم تصدير إلى Excel أو CSV
4. **طباعة محسنة**: خيارات طباعة أكثر تقدماً
5. **حفظ سحابي**: خيار حفظ الفواتير في السحابة (اختياري)

## إصلاح مشكلة QR Code ✅

### المشكلة السابقة
كان QR Code في مولد الفواتير ZATCA **غير قابل للقراءة** لأنه كان مجرد رسم تمثيلي (pseudo-random pattern) باستخدام Canvas وليس QR Code حقيقي.

### الحل المُطبق
1. **استبدال التنفيذ المزيف:** تم استبدال الكود الذي ينشئ نمط عشوائي بتنفيذ حقيقي
2. **استخدام QR Server API:** تم استخدام خدمة موثوقة لإنشاء QR Code حقيقي قابل للقراءة
3. **إضافة أداة فك التشفير:** تم إنشاء أداة منفصلة لفك تشفير QR Code واختباره
4. **تحسين التشخيص:** إضافة معلومات تشخيصية لمساعدة المطورين

### الأدوات الجديدة
- **مولد الفواتير المُحدث:** QR Code حقيقي قابل للقراءة
- **أداة فك تشفير QR Code:** للتحقق من صحة البيانات المُستخرجة (`/tools/zatca-qr-decoder`)
- **ملف اختبار HTML:** لاختبار QR Code خارج التطبيق (`test-qr-zatca.html`)

### كيفية الاختبار
1. افتح مولد الفواتير وأدخل بيانات تجريبية
2. انسخ البيانات المُشفرة من QR Code (ستظهر في console في وضع التطوير)
3. استخدم أداة فك التشفير للتحقق من البيانات
4. اختبر قراءة QR Code باستخدام هاتفك أو أي قارئ QR Code
5. اختبر تحميل PDF - يجب أن ينزل ملف PDF حقيقي

## إصلاح مشكلة تحميل PDF ✅

### المشكلة السابقة
كان زر "تحميل PDF" يفتح نافذة الطباعة بدلاً من تحميل ملف PDF مباشرة.

### الحل المُطبق
1. **إضافة مكتبات PDF:** تم تثبيت `jsPDF` و `html2canvas`
2. **تحويل HTML إلى صورة:** استخدام html2canvas لتحويل الفاتورة إلى صورة عالية الجودة
3. **إنشاء PDF حقيقي:** استخدام jsPDF لإنشاء ملف PDF يحتوي على الفاتورة
4. **تحسين الجودة:** إعدادات محسنة للحصول على أفضل جودة للنص العربي
5. **اسم ملف ذكي:** تسمية الملف تلقائياً بناءً على رقم الفاتورة والتاريخ

### المميزات الجديدة
- ✅ تحميل ملف PDF حقيقي بدلاً من فتح نافذة الطباعة
- ✅ جودة عالية للنصوص العربية والإنجليزية
- ✅ دعم الفواتير الطويلة (صفحات متعددة)
- ✅ اسم ملف تلقائي: `فاتورة-[رقم الفاتورة]-[التاريخ].pdf`
- ✅ حالة تحميل تفاعلية (يظهر "جاري إنشاء PDF...")
- ✅ معالجة الأخطاء مع رسائل واضحة

## الخلاصة

✅ **تم إصلاح مشكلة QR Code بنجاح!**

مولد الفواتير الإلكترونية أصبح الآن متوافقاً بالكامل مع متطلبات هيئة الزكاة والضريبة والجمارك. الأداة تنتج QR Code حقيقي قابل للقراءة ومتوافق مع جميع قارئات QR Code. جميع المتطلبات التقنية والقانونية تم تلبيتها، والأداة تعمل بكفاءة عالية مع واجهة مستخدم ممتازة.

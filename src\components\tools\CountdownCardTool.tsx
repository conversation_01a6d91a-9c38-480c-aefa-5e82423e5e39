'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { differenceInSeconds } from 'date-fns';
import type { NationalEvent } from '@/lib/dates';

interface CountdownCardToolProps {
  initialData: NationalEvent;
}

function formatTimeLeft(totalSeconds: number) {
  // إذا كان الوقت سالباً أو صفر، أرجع قيم صفر
  if (totalSeconds <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: true };
  }

  const days = Math.floor(totalSeconds / 86400);
  const hours = Math.floor((totalSeconds % 86400) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  return { days, hours, minutes, seconds, isExpired: false };
}


export function CountdownCardTool({ initialData }: CountdownCardToolProps) {
  const [event, setEvent] = useState(initialData);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: false });
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!event.nextDate || !mounted) return;

    const calculateTime = () => {
      try {
        const now = new Date();
        const totalSeconds = differenceInSeconds(event.nextDate!, now);

        // No need to check for negative here, as the `isDate` flag handles it
        setTimeLeft(formatTimeLeft(totalSeconds));
      } catch (error) {
        console.error('Error calculating countdown time:', error);
      }
    };

    calculateTime();
    const timer = setInterval(calculateTime, 1000);
    return () => clearInterval(timer);
  }, [event.nextDate, mounted]);

  if (!event.nextDate) {
    return (
        <Card className="w-full text-center">
            <CardHeader>
                <CardTitle>{event.name}</CardTitle>
                <CardDescription>لا يوجد موعد قادم مجدول حالياً.</CardDescription>
            </CardHeader>
        </Card>
    )
  }

  return (
    <Card className="w-full text-center">
      <CardHeader>
        <CardTitle className="text-2xl md:text-3xl font-headline">{event.name}</CardTitle>
        {event.isDate ? (
            <CardDescription className="text-base">
                يصادف يوم {event.nextDate.toLocaleDateString('ar-SA-u-nu-latn', { weekday: 'long' })},
                الموافق {event.nextDate.toLocaleDateString('ar-SA-u-nu-latn', { year: 'numeric', month: 'long', day: 'numeric' })}
                {event.hijriDate && ` - ${event.hijriDate}`}
            </CardDescription>
        ) : (
            <CardDescription className="text-base">
                الوقت المتبقي حتى {event.nextDate.toLocaleDateString('ar-SA-u-nu-latn', { month: 'long', day: 'numeric' })}
            </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        {event.isDate || timeLeft.isExpired ? (
            <div className="text-3xl md:text-4xl font-bold text-primary p-4 bg-primary/10 rounded-lg">
                {event.isDate ? 'حدث اليوم!' : 'انتهى الحدث!'}
            </div>
        ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-primary/10 rounded-lg">
                    <p className="text-4xl md:text-5xl font-bold font-mono text-primary">{timeLeft.days}</p>
                    <p className="text-sm text-muted-foreground">أيام</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                    <p className="text-4xl md:text-5xl font-bold font-mono text-primary">{timeLeft.hours}</p>
                    <p className="text-sm text-muted-foreground">ساعات</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                    <p className="text-4xl md:text-5xl font-bold font-mono text-primary">{timeLeft.minutes}</p>
                    <p className="text-sm text-muted-foreground">دقائق</p>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                    <p className="text-4xl md:text-5xl font-bold font-mono text-primary">{timeLeft.seconds}</p>
                    <p className="text-sm text-muted-foreground">ثواني</p>
                </div>
            </div>
        )}
      </CardContent>
    </Card>
  );
}
CountdownCardTool.displayName = "CountdownCardTool";


'use client';

import { useState } from 'react';
import { useForm, useFieldArray, useFormContext, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlusCircle, Trash2, Calculator, Users, HandCoins, Utensils, Camera, PartyPopper, Mic, Gem, Mail, Sparkles } from 'lucide-react';
import { Separator } from '../ui/separator';

const requiredNumber = (message = "أدخل قيمة أو صفر.") => 
  z.coerce.number({ invalid_type_error: message }).nonnegative("لا يمكن أن تكون القيمة سالبة.").default(0);

const itemSchema = z.object({
  name: z.string(),
  cost: requiredNumber(),
});

const categorySchema = z.object({
  items: z.array(itemSchema),
  total: z.number().default(0),
});

const formSchema = z.object({
  guestCount: z.coerce.number({required_error: "الرجاء إدخال عدد الضيوف."}).int("يجب أن يكون عدد الضيوف رقمًا صحيحًا.").min(1, "يجب أن يكون عدد الضيوف 1 على الأقل."),
  venue: categorySchema,
  food: categorySchema,
  decor: categorySchema,
  photoVideo: categorySchema,
  entertainment: categorySchema,
  attire: categorySchema,
  invitations: categorySchema,
  misc: categorySchema,
});

type FormValues = z.infer<typeof formSchema>;
type CategoryKey = keyof Omit<FormValues, 'guestCount'>;

const initialCategories: Record<CategoryKey, { name: string; icon: React.ElementType; items: { name: string }[] }> = {
  venue: { name: 'القاعة والمكان', icon: HandCoins, items: [{ name: 'إيجار القاعة' }, { name: 'تأمين' }, { name: 'خدمة صف السيارات' }] },
  food: { name: 'الطعام والشراب', icon: Utensils, items: [{ name: 'تكلفة الشخص (بوفيه/قائمة)' }, { name: 'المشروبات' }, { name: 'كيكة المناسبة' }] },
  decor: { name: 'الكوشة والديكور', icon: Sparkles, items: [{ name: 'تصميم الكوشة' }, { name: 'زهور وطاولات' }, { name: 'إضاءة' }] },
  photoVideo: { name: 'التصوير والفيديو', icon: Camera, items: [{ name: 'مصور فوتوغرافي' }, { name: 'مصور فيديو' }] },
  entertainment: { name: 'الترفيه', icon: Mic, items: [{ name: 'دي جي أو فرقة موسيقية' }, { name: 'مؤثرات صوتية/بصرية' }] },
  attire: { name: 'الملابس والمظهر', icon: Gem, items: [{ name: 'فستان/بدلة' }, { name: 'مكياج وتسريحة' }] },
  invitations: { name: 'الدعوات والهدايا', icon: Mail, items: [{ name: 'تصميم وطباعة الدعوات' }, { name: 'هدايا الضيوف (توزيعات)' }] },
  misc: { name: 'تكاليف إضافية', icon: PartyPopper, items: [{ name: 'تنسيق الحدث' }] },
};


const CategoryCard = ({ categoryKey, title, icon: Icon }: { categoryKey: CategoryKey, title: string, icon: React.ElementType }) => {
  const { control, watch } = useFormContext<FormValues>();
  const { fields } = useFieldArray({ control, name: `${categoryKey}.items` });
  
  const items = watch(`${categoryKey}.items`);
  const categoryTotal = items.reduce((sum, item) => sum + (parseFloat(String(item.cost)) || 0), 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <Icon className="w-5 h-5 text-primary" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {fields.map((field, index) => (
          <FormField
            key={field.id}
            control={control}
            name={`${categoryKey}.items.${index}.cost`}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center justify-between gap-2">
                  <FormLabel className="flex-1">{items[index].name}</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" className="w-32 text-left" dir="ltr" {...formField} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
        <Separator />
        <div className="flex justify-between items-center font-bold">
          <span>مجموع {title}</span>
          <span>{categoryTotal.toLocaleString()} ريال</span>
        </div>
      </CardContent>
    </Card>
  );
};


export function EventCostCalculatorTool() {
  const [totalCost, setTotalCost] = useState(0);
  const [costPerGuest, setCostPerGuest] = useState(0);

  const formMethods = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      guestCount: 50,
      ...Object.fromEntries(
        Object.entries(initialCategories).map(([key, value]) => [
          key, { items: value.items.map(item => ({ ...item, cost: 0 })) }
        ])
      )
    },
  });

  const onSubmit = (data: FormValues) => {
    const total = Object.values(data)
        .filter((value: any): value is { items: { cost: number }[] } => value && Array.isArray(value.items))
        .flatMap((category) => category.items.map((item) => parseFloat(String(item.cost)) || 0))
        .reduce((sum, cost) => sum + cost, 0);

    setTotalCost(total);
    setCostPerGuest(data.guestCount > 0 ? total / data.guestCount : 0);
  };
  
  return (
    <div className="w-full">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl">حاسبة تكلفة الحفلات والمناسبات</CardTitle>
        <CardDescription className="text-lg">خطط لميزانية فرحك، تخرجك، أو أي مناسبة سعيدة بسهولة ودقة.</CardDescription>
      </CardHeader>

      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-8">
            <div className="py-4">
                <Card>
                    <CardContent className="p-4 space-y-4">
                        <FormField
                            control={formMethods.control}
                            name="guestCount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-lg">كم عدد ضيوفك؟</FormLabel>
                                    <FormControl>
                                        <Input type="number" min="1" className="text-lg h-12" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                         <Button type="submit" className="w-full h-12 text-lg">
                            <Calculator className="ml-2 h-5 w-5" />
                            احسب التكلفة الإجمالية
                        </Button>
                    </CardContent>
                </Card>
                {totalCost > 0 && (
                    <Card className="mt-4 border-primary bg-primary/5">
                        <CardHeader>
                            <CardTitle className="text-center text-primary">ملخص التكاليف</CardTitle>
                        </CardHeader>
                        <CardContent className="flex justify-around text-center">
                            <div>
                                <p className="text-sm text-muted-foreground">التكلفة الإجمالية</p>
                                <p className="text-2xl font-bold">{totalCost.toLocaleString()} ريال</p>
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">التكلفة لكل ضيف</p>
                                <p className="text-2xl font-bold">{costPerGuest.toFixed(2)} ريال</p>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(initialCategories).map(([key, { name, icon }]) => (
                    <CategoryCard
                        key={key}
                        categoryKey={key as CategoryKey}
                        title={name}
                        icon={icon}
                    />
                ))}
            </div>
        </form>
      </FormProvider>
    </div>
  );
}

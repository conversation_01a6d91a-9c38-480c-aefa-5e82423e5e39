
'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, Alert<PERSON><PERSON>gle, ArrowRightLeft, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';

export function WebpToPngTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'image/webp') {
      setError('يرجى اختيار ملف بصيغة WebP.');
      return;
    }
    
    setSelectedFile(file);
    setError(null);
    setConvertedImage(null);
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const convertImage = () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setError(null);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.src = e.target?.result as string;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          setError('فشل في إنشاء سياق الرسم.');
          setIsProcessing(false);
          return;
        }
        ctx.drawImage(img, 0, 0);
        
        try {
          const pngUrl = canvas.toDataURL('image/png');
          setConvertedImage(pngUrl);
          toast({ title: 'تم تحويل الصورة بنجاح!' });
        } catch (err) {
          setError('فشل في تحويل الصورة. يرجى المحاولة مرة أخرى.');
        } finally {
          setIsProcessing(false);
        }
      };
      img.onerror = () => {
        setError('فشل في تحميل صورة WebP. قد لا يكون متصفحك يدعمها بشكل كامل.');
        setIsProcessing(false);
      };
    };
    reader.readAsDataURL(selectedFile);
  };

  const clearFile = () => {
    setSelectedFile(null);
    setError(null);
    setConvertedImage(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل من WebP إلى PNG</CardTitle>
        <CardDescription>حوّل صور WebP الحديثة إلى صيغة PNG المتوافقة مع جميع الأنظمة والتطبيقات.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر صورة WebP للتحويل</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
                <Button>اختر صورة</Button>
              </div>
            </div>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/webp" className="hidden" />
        </div>
        
        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {selectedFile && (
          <div className="flex flex-col sm:flex-row gap-2">
            <Button onClick={convertImage} disabled={isProcessing} className="flex-1">
              {isProcessing ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <ArrowRightLeft className="ml-2 h-4 w-4" />}
              {isProcessing ? 'جاري التحويل...' : 'تحويل إلى PNG'}
            </Button>
            <Button onClick={clearFile} variant="outline" disabled={isProcessing} className="flex-1"><Trash2 className="ml-2 h-4 w-4" /> مسح</Button>
          </div>
        )}
        
        {convertedImage && (
          <div className="mt-6 text-center space-y-4">
             <h3 className="font-semibold">الصورة المحولة (PNG)</h3>
            <img src={convertedImage} alt="Converted" className="max-w-full rounded-md border" />
            <Button onClick={() => saveAs(convertedImage, `${selectedFile?.name.split('.')[0]}.png`)} className="w-full">
              <Download className="ml-2 h-4 w-4" />
              تحميل الصورة
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

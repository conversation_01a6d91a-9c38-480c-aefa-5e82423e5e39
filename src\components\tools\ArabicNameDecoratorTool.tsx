
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Copy, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Switch } from '../ui/switch';
import { ScrollArea } from '../ui/scroll-area';

const FormSchema = z.object({
  text: z.string().min(1, { message: 'الرجاء إدخال اسم للزخرفة.' }),
  addKashida: z.boolean().default(false),
});

const applyKashida = (text: string): string => {
  const kashida = 'ـ';
  // Avoid adding kashida before or after spaces, or after characters that don't connect forwards.
  const noConnectChars = 'اأإآدذرزوؤ';
  let result = '';
  for (let i = 0; i < text.length; i++) {
    result += text[i];
    if (
      i < text.length - 1 &&
      text[i] !== ' ' &&
      text[i + 1] !== ' ' &&
      !noConnectChars.includes(text[i])
    ) {
      result += kashida;
    }
  }
  return result;
};

const decorationMaps: { [key: string]: { [key: string]: string } } = {
  symbols1: { 'ا': 'Ã', 'ب': 'β', 'ت': 'Ť', 'ث': 'ŤĤ', 'ج': 'Ĵ', 'ح': 'Ĥ', 'خ': 'ĶĤ', 'د': 'Ď', 'ذ': 'ĎĤ', 'ر': 'Ř', 'ز': 'Ż', 'س': 'Ŝ', 'ش': 'ŜĤ', 'ص': 'Š', 'ض': 'Ď', 'ط': 'Ŧ', 'ظ': 'Ż', 'ع': 'Ă', 'غ': 'Ğ', 'ف': 'Ŧ', 'ق': 'Q', 'ك': 'Ķ', 'ل': 'Ĺ', 'م': 'M', 'ن': 'Ň', 'ه': 'Ĥ', 'و': 'Ŵ', 'ي': 'Ў', 'ة': '─', 'أ': 'Å', 'إ': 'Ễ', 'آ': 'Ā' },
  symbols2: { 'ا': 'Ạ', 'ب': 'B', 'ت': 'T', 'ث': 'T', 'ج': 'J', 'ح': 'H', 'خ': 'K', 'د': 'D', 'ذ': 'D', 'ر': 'R', 'ز': 'Z', 'س': 'S', 'ش': 'S', 'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z', 'ع': 'A', 'غ': 'G', 'ف': 'F', 'ق': 'Q', 'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N', 'ه': 'H', 'و': 'W', 'ي': 'Y', 'ة': 'A', 'أ': 'A', 'إ': 'E', 'آ': 'A' },
  circles: { 'ا': 'ⓐ', 'ب': 'ⓑ', 'ت': 'ⓣ', 'ث': 'ⓣ', 'ج': 'ⓙ', 'ح': 'ⓗ', 'خ': 'ⓚ', 'د': 'ⓓ', 'ذ': 'ⓓ', 'ر': 'ⓡ', 'ز': 'ⓩ', 'س': 'ⓢ', 'ش': 'ⓢ', 'ص': 'ⓢ', 'ض': 'ⓓ', 'ط': 'ⓣ', 'ظ': 'ⓩ', 'ع': 'ⓐ', 'غ': 'ⓖ', 'ف': 'ⓕ', 'ق': 'ⓠ', 'ك': 'ⓚ', 'ل': 'ⓛ', 'م': 'ⓜ', 'ن': 'ⓝ', 'ه': 'ⓗ', 'و': 'w', 'ي': 'ⓨ', 'ة': 'ⓐ', 'أ': 'ⓐ', 'إ': 'ⓔ', 'آ': 'ⓐ' },
  fancy1: { 'ا': '𝒶', 'ب': '𝒷', 'ت': '𝓉', 'ث': '𝓉𝒽', 'ج': '𝒿', 'ح': '𝒽', 'خ': '𝓀𝒽', 'د': '𝒹', 'ذ': '𝒹𝒽', 'ر': '𝓇', 'ز': '𝓏', 'س': '𝓈', 'ش': '𝓈𝒽', 'ص': '𝓈', 'ض': '𝒹', 'ط': '𝓉', 'ظ': '𝓏', 'ع': '𝒶', 'غ': '𝑔', 'ف': '𝒻', 'ق': '𝓆', 'ك': '𝓀', 'ل': '𝓁', 'م': '𝓂', 'ن': '𝓃', 'ه': '𝒽', 'و': '𝓌', 'ي': '𝓎', 'ة': '𝒶', 'أ': '𝒶', 'إ': '𝑒', 'آ': '𝒶' },
  fancy2: { 'ا': '𝔞', 'ب': '𝔟', 'ت': '𝔱', 'ث': '𝔱𝔥', 'ج': '𝔧', 'ح': '𝔥', 'خ': '𝔨𝔥', 'د': '𝔡', 'ذ': '𝔡𝔥', 'ر': '𝔯', 'ز': '𝔷', 'س': '𝔰', 'ش': '𝔰𝔥', 'ص': '𝔰', 'ض': '𝔡', 'ط': '𝔱', 'ظ': '𝔷', 'ع': '𝔞', 'غ': '𝔤', 'ف': '𝔣', 'ق': '𝔮', 'ك': '𝔨', 'ل': '𝔩', 'م': '𝔪', 'ن': '𝔫', 'ه': '𝔥', 'و': '𝔴', 'ي': '𝔶', 'ة': '𝔞', 'أ': '𝔞', 'إ': '𝔢', 'آ': '𝔞' },
  bold: { 'ا': '𝐚', 'ب': '𝐛', 'ت': '𝐭', 'ث': '𝐭𝐡', 'ج': '𝐣', 'ح': '𝐡', 'خ': '𝐤𝐡', 'د': '𝐝', 'ذ': '𝐝𝐡', 'ر': '𝐫', 'ز': '𝐳', 'س': '𝐬', 'ش': '𝐬𝐡', 'ص': '𝐬', 'ض': '𝐝', 'ط': '𝐭', 'ظ': '𝐳', 'ع': '𝐚', 'غ': '𝐠', 'ف': '𝐟', 'ق': '𝐪', 'ك': '𝐤', 'ل': '𝐥', 'م': '𝐦', 'ن': '𝐧', 'ه': '𝐡', 'و': '𝐰', 'ي': '𝐲', 'ة': '𝐚', 'أ': '𝐚', 'إ': '𝐞', 'آ': '𝐚' },
  italic: { 'ا': '𝑎', 'ب': '𝑏', 'ت': '𝑡', 'ث': '𝑡ℎ', 'ج': '𝑗', 'ح': 'ℎ', 'خ': '𝑘ℎ', 'د': '𝑑', 'ذ': '𝑑ℎ', 'ر': '𝑟', 'ز': '𝑧', 'س': '𝑠', 'ش': '𝑠ℎ', 'ص': '𝑠', 'ض': '𝑑', 'ط': '𝑡', 'ظ': '𝑧', 'ع': '𝑎', 'غ': '𝑔', 'ف': '𝑓', 'ق': '𝑞', 'ك': '𝑘', 'ل': '𝑙', 'م': '𝑚', 'ن': '𝑛', 'ه': 'ℎ', 'و': '𝑤', 'ي': '𝑦', 'ة': '𝑎', 'أ': '𝑎', 'إ': '𝑒', 'آ': '𝑎' },
  squares: { 'ا': '⬜', 'ب': '⬛', 'ت': '▫', 'ث': '▪', 'ج': '◻', 'ح': '◼', 'خ': '▢', 'د': '▣', 'ذ': '⬜', 'ر': '⬛', 'ز': '▫', 'س': '▪', 'ش': '◻', 'ص': '◼', 'ض': '▢', 'ط': '▣', 'ظ': '⬜', 'ع': '⬛', 'غ': '▫', 'ف': '▪', 'ق': '◻', 'ك': '◼', 'ل': '▢', 'م': '▣', 'ن': '⬜', 'ه': '⬛', 'و': '▫', 'ي': '▪', 'ة': '◻', 'أ': '◼', 'إ': '▢', 'آ': '▣' },
  double: { 'ا': '𝕒', 'ب': '𝕓', 'ت': '𝕥', 'ث': '𝕥𝕙', 'ج': '𝕛', 'ح': '𝕙', 'خ': '𝕜𝕙', 'د': '𝕕', 'ذ': '𝕕𝕙', 'ر': '𝕣', 'ز': '𝕫', 'س': '𝕤', 'ش': '𝕤𝕙', 'ص': '𝕤', 'ض': '𝕕', 'ط': '𝕥', 'ظ': '𝕫', 'ع': '𝕒', 'غ': '𝕘', 'ف': '𝕗', 'ق': '𝕢', 'ك': '𝕜', 'ل': '𝕝', 'م': '𝕞', 'ن': '𝕟', 'ه': '𝕙', 'و': '𝕨', 'ي': '𝕪', 'ة': '𝕒', 'أ': '𝕒', 'إ': '𝕖', 'آ': '𝕒' },
  underline: { 'ا': 'a̲', 'ب': 'b̲', 'ت': 't̲', 'ث': 't̲h̲', 'ج': 'j̲', 'ح': 'h̲', 'خ': 'k̲h̲', 'د': 'd̲', 'ذ': 'd̲h̲', 'ر': 'r̲', 'ز': 'z̲', 'س': 's̲', 'ش': 's̲h̲', 'ص': 's̲', 'ض': 'd̲', 'ط': 't̲', 'ظ': 'z̲', 'ع': 'a̲', 'غ': 'g̲', 'ف': 'f̲', 'ق': 'q̲', 'ك': 'k̲', 'ل': 'l̲', 'م': 'm̲', 'ن': 'n̲', 'ه': 'h̲', 'و': 'w̲', 'ي': 'y̲', 'ة': 'a̲', 'أ': 'a̲', 'إ': 'e̲', 'آ': 'a̲' },
  strikethrough: { 'ا': 'a̶', 'ب': 'b̶', 'ت': 't̶', 'ث': 't̶h̶', 'ج': 'j̶', 'ح': 'h̶', 'خ': 'k̶h̶', 'د': 'd̶', 'ذ': 'd̶h̶', 'ر': 'r̶', 'ز': 'z̶', 'س': 's̶', 'ش': 's̶h̶', 'ص': 's̶', 'ض': 'd̶', 'ط': 't̶', 'ظ': 'z̶', 'ع': 'a̶', 'غ': 'g̶', 'ف': 'f̶', 'ق': 'q̶', 'ك': 'k̶', 'ل': 'l̶', 'م': 'm̶', 'ن': 'n̶', 'ه': 'h̶', 'و': 'w̶', 'ي': 'y̶', 'ة': 'a̶', 'أ': 'a̶', 'إ': 'e̶', 'آ': 'a̶' },
};

const applyDecoration = (text: string, map: { [key: string]: string }): string => {
  return [...text].map(char => map[char] || char).join('');
};

const surrounders: { [key: string]: [string, string] } = {
  sparkles: ['✦', '✦'],
  hearts: ['♡', '♡'],
  stars: ['★', '★'],
  doubleArrow: ['《', '》'],
  curved: ['︵', '︶'],
  diamonds: ['◆', '◆'],
  crown: ['♔', '♔'],
  fire: ['※', '※'],
  lightning: ['⟡', '⟡'],
  moon: ['☽', '☽'],
  sun: ['☀', '☀'],
  flower: ['❀', '❀'],
  butterfly: ['⟐', '⟐'],
  gem: ['◊', '◊'],
  magic: ['✧', '✧'],
  brackets: ['【', '】'],
  parentheses: ['（', '）'],
  angles: ['〈', '〉'],
  waves: ['～', '～'],
  dots: ['•', '•'],
  arrows: ['→', '←'],
  lines: ['━', '━'],
  dashes: ['─', '─'],
  circles: ['○', '○'],
  squares: ['□', '□'],
  triangles: ['△', '△'],
  crosses: ['✕', '✕'],
  plus: ['✚', '✚'],
  asterisk: ['✱', '✱'],
  bullet: ['◉', '◉'],
  diamond2: ['◇', '◇'],
  star2: ['☆', '☆'],
  heart2: ['♥', '♥'],
};

export function ArabicNameDecoratorTool() {
  const [results, setResults] = useState<string[]>([]);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
      addKashida: false,
    },
  });

  const onSubmit = (data: z.infer<typeof FormSchema>) => {
    let baseText = data.text;
    if (data.addKashida) {
      baseText = applyKashida(baseText);
    }

    const decoratedResults: string[] = [];

    // Basic text
    decoratedResults.push(baseText);

    // Simple decorations
    Object.entries(decorationMaps).forEach(([name, map]) => {
      decoratedResults.push(applyDecoration(baseText, map));
    });

    // Simple surrounders
    Object.entries(surrounders).forEach(([name, [start, end]]) => {
      decoratedResults.push(`${start} ${baseText} ${end}`);
    });

    // Advanced combinations - decorations with surrounders
    const popularDecorations = ['symbols1', 'fancy1', 'bold', 'circles'];
    const popularSurrounders = ['sparkles', 'hearts', 'stars', 'crown', 'fire'];

    popularDecorations.forEach(decorationName => {
      popularSurrounders.forEach(surrounderName => {
        const decoration = decorationMaps[decorationName];
        const [start, end] = surrounders[surrounderName];
        if (decoration) {
          decoratedResults.push(`${start} ${applyDecoration(baseText, decoration)} ${end}`);
        }
      });
    });

    // Special patterns
    decoratedResults.push(`✧･ﾟ: *✧･ﾟ:* ${baseText} *:･ﾟ✧*:･ﾟ✧`);
    decoratedResults.push(`♡ ${baseText} ♡`);
    decoratedResults.push(`◦•●◉✿ ${baseText} ✿◉●•◦`);
    decoratedResults.push(`▁ ▂ ▄ ▅ ▆ ▇ █ ${baseText} █ ▇ ▆ ▅ ▄ ▂ ▁`);
    decoratedResults.push(`°º¤ø,¸¸,ø¤º°\`°º¤ø,¸ ${baseText} ¸,ø¤º°\`°º¤ø,¸¸,ø¤º°`);
    decoratedResults.push(`ıllıllı ${baseText} ıllıllı`);
    decoratedResults.push(`•´¯\`•. ${baseText} .•´¯\`•`);
    decoratedResults.push(`¸,ø¤º°\`°º¤ø,¸¸,ø¤º° ${baseText} °º¤ø,¸¸,ø¤º°\`°º¤ø,¸`);

    // Reverse text
    decoratedResults.push([...baseText].reverse().join(''));

    // Spaced text
    decoratedResults.push([...baseText].join(' '));
    decoratedResults.push([...baseText].join('  '));
    decoratedResults.push([...baseText].join(' • '));
    decoratedResults.push([...baseText].join(' ◦ '));
    decoratedResults.push([...baseText].join(' ★ '));

    setResults(decoratedResults);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'تم النسخ بنجاح!',
        description: `"${text}" تم نسخه إلى الحافظة.`,
      });
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>زخرفة الأسماء العربية</CardTitle>
        <CardDescription>
          أدخل اسمك أو أي نص عربي لترى مجموعة متنوعة من الأنماط المزخرفة الجاهزة للنسخ.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>النص الأصلي</FormLabel>
                    <FormControl>
                      <Input placeholder="اكتب الاسم هنا" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" size="lg" className="w-full">
                <Wand2 className="ml-2 h-4 w-4" />
                زخرفة
              </Button>
            </div>
            <FormField
              control={form.control}
              name="addKashida"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>تطويل الحروف (كشيدة)</FormLabel>
                    <FormDescription>
                      إضافة تطويل بين الحروف لإعطاء شكل جمالي.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>

        {results.length > 0 && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج:</h3>
            <ScrollArea className="h-72 w-full rounded-md border p-4 bg-muted/50">
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between gap-4 p-3 bg-background rounded-md shadow-sm">
                    <p className="text-lg font-mono flex-1 text-right" dir="rtl">{result}</p>
                    <Button variant="outline" size="icon" onClick={() => copyToClipboard(result)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

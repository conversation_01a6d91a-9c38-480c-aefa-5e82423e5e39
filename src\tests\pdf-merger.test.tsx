import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PdfMergerTool } from '@/components/tools/PdfMergerTool';
import { useToast } from '@/hooks/use-toast';

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}));

// Mock PDF-lib
const mockPDFDocument = {
  create: jest.fn(),
  load: jest.fn(),
  copyPages: jest.fn(),
  addPage: jest.fn(),
  save: jest.fn(),
  getPageIndices: jest.fn(() => [0]),
};

// Mock window.PDFLib
Object.defineProperty(window, 'PDFLib', {
  value: {
    PDFDocument: mockPDFDocument,
  },
  writable: true,
});

describe('PdfMergerTool', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the PDF merger tool correctly', () => {
    render(<PdfMergerTool />);
    
    expect(screen.getByText('أداة دمج ملفات PDF')).toBeInTheDocument();
    expect(screen.getByText('اختر ملفات PDF للدمج')).toBeInTheDocument();
    expect(screen.getByText('كيفية الاستخدام')).toBeInTheDocument();
  });

  it('shows loading state when PDF-lib is not loaded', () => {
    // Temporarily remove PDFLib from window
    const originalPDFLib = window.PDFLib;
    delete (window as any).PDFLib;
    
    render(<PdfMergerTool />);
    
    expect(screen.getByText('جاري تحميل مكتبة PDF...')).toBeInTheDocument();
    
    // Restore PDFLib
    window.PDFLib = originalPDFLib;
  });

  it('handles file selection correctly', async () => {
    const mockToast = jest.fn();
    (useToast as jest.Mock).mockReturnValue({ toast: mockToast });

    render(<PdfMergerTool />);
    
    const fileInput = screen.getByRole('button', { name: /اختر ملفات PDF/i });
    
    // Create a mock PDF file
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    // Mock the file input change event
    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(input);
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'تم إضافة الملفات',
        description: 'تم إضافة 1 ملف PDF جديد.',
      });
    });
  });

  it('shows error for non-PDF files', async () => {
    render(<PdfMergerTool />);
    
    // Create a mock non-PDF file
    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    
    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(input);
    
    await waitFor(() => {
      expect(screen.getByText('يرجى اختيار ملفات PDF صالحة.')).toBeInTheDocument();
    });
  });

  it('shows error for oversized files', async () => {
    render(<PdfMergerTool />);
    
    // Create a mock oversized PDF file (11MB)
    const file = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { type: 'application/pdf' });
    
    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(input);
    
    await waitFor(() => {
      expect(screen.getByText('بعض الملفات كبيرة جداً. يرجى اختيار ملفات أصغر من 10 ميجابايت.')).toBeInTheDocument();
    });
  });

  it('disables merge button when less than 2 files are selected', () => {
    render(<PdfMergerTool />);
    
    // Add one file
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(input);
    
    // The merge button should be disabled or show appropriate message
    expect(screen.getByText('يجب اختيار ملفين على الأقل للدمج')).toBeInTheDocument();
  });
});

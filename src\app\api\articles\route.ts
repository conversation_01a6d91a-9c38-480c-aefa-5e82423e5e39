import { NextRequest, NextResponse } from 'next/server';
import {
  getArticles,
  createArticle
} from '@/lib/articles-supabase';
import { generateSlug } from '@/lib/supabase';

interface ArticleFilters {
  status?: string;
  category?: string;
  featured?: boolean;
  search?: string;
}
import { CreateArticleData, ArticleFilters } from '@/types/article';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check if it's a search request
    const searchTerm = searchParams.get('search');

    // Regular article listing with filters
    const filters: ArticleFilters = {};
    const pageSize = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');

    if (searchParams.get('category')) {
      filters.category = searchParams.get('category')!;
    }
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status');
    }
    if (searchParams.get('featured')) {
      filters.featured = searchParams.get('featured') === 'true';
    }
    if (searchTerm) {
      filters.search = searchTerm;
    }

    const result = await getArticles({
      ...filters,
      limit: pageSize,
      offset: (page - 1) * pageSize
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/articles:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المقالات' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'content', 'category', 'author', 'authorId'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `الحقل ${field} مطلوب` },
          { status: 400 }
        );
      }
    }

    // Generate slug if not provided
    if (!body.slug) {
      body.slug = generateSlug(body.title);
    }

    // Set default values
    const articleData: CreateArticleData = {
      title: body.title,
      slug: body.slug,
      description: body.description,
      content: body.content,
      category: body.category,
      tags: body.tags || [],
      author: body.author,
      authorId: body.authorId,
      readTime: body.readTime || '5 دقائق',
      status: body.status || 'draft',
      featured: body.featured || false,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      seoKeywords: body.seoKeywords || [],
      relatedTools: body.relatedTools || [],
      relatedArticles: body.relatedArticles || [],
      imageUrl: body.imageUrl,
      imageAlt: body.imageAlt,
    };

    const articleId = await createArticle(articleData);
    
    return NextResponse.json(
      { id: articleId, message: 'تم إنشاء المقال بنجاح' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/articles:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المقال' },
      { status: 500 }
    );
  }
}


'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const MINUTES_IN_HOUR = 60;

export function MinutesToHoursConverterTool() {
  const [minutes, setMinutes] = useState('60');
  const [hours, setHours] = useState('1');

  const handleMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinutes(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setHours((numValue / MINUTES_IN_HOUR).toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setHours('');
    }
  };

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setHours(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setMinutes((numValue * MINUTES_IN_HOUR).toLocaleString('en-US', {maximumFractionDigits: 2, useGrouping: false}));
    } else {
      setMinutes('');
    }
  };

  const handleSwap = () => {
    const currentMinutes = minutes;
    const currentHours = hours;
    setMinutes(currentHours);
    setHours(currentMinutes);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من دقائق إلى ساعات (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري للوقت.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 ساعة = 60 دقيقة
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="minutes" className="text-sm font-medium mb-2 block">
              دقائق
            </label>
            <Input
              id="minutes"
              type="number"
              value={minutes}
              onChange={handleMinutesChange}
              placeholder="أدخل الدقائق"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="hours" className="text-sm font-medium mb-2 block">
              ساعات
            </label>
            <Input
              id="hours"
              type="number"
              value={hours}
              onChange={handleHoursChange}
              placeholder="أدخل الساعات"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

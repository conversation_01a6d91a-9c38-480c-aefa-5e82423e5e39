# نظام تقييم الأدوات - Tool Rating System

## نظرة عامة

تم تطوير نظام تقييم شامل للأدوات يسمح للمستخدمين بتقييم كل أداة من 1 إلى 5 نجوم مع إمكانية إضافة تعليقات.

## المكونات الرئيسية

### 1. قاعدة البيانات (Supabase)

#### الجداول:
- **`tools`**: معلومات الأدوات الأساسية
- **`tool_ratings`**: التقييمات الفردية من المستخدمين
- **`tool_rating_stats`**: إحصائيات التقييمات المجمعة

#### المخطط:
```sql
-- جدول الأدوات
CREATE TABLE tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول التقييمات
CREATE TABLE tool_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_slug TEXT NOT NULL REFERENCES tools(slug) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    user_session TEXT NOT NULL,
    user_ip TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول إحصائيات التقييمات
CREATE TABLE tool_rating_stats (
    tool_slug TEXT PRIMARY KEY REFERENCES tools(slug) ON DELETE CASCADE,
    total_ratings INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    rating_1_count INTEGER DEFAULT 0,
    rating_2_count INTEGER DEFAULT 0,
    rating_3_count INTEGER DEFAULT 0,
    rating_4_count INTEGER DEFAULT 0,
    rating_5_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. مكتبة API (`src/lib/ratings.ts`)

#### الوظائف الرئيسية:
- `getToolRatingStats(toolSlug)`: جلب إحصائيات التقييم
- `addToolRating(toolSlug, rating, comment)`: إضافة تقييم جديد
- `getUserRating(toolSlug)`: جلب تقييم المستخدم الحالي
- `getToolRatings(toolSlug, limit)`: جلب التقييمات الأخيرة

#### إدارة الجلسات:
- استخدام `localStorage` لحفظ معرف الجلسة
- تتبع تقييمات المستخدم بدون تسجيل دخول
- منع التقييم المتكرر من نفس الجلسة

### 3. مكونات React

#### `ToolRating.tsx` - المكون الرئيسي:
- عرض إحصائيات التقييم
- واجهة تقييم تفاعلية بالنجوم
- نموذج إضافة تعليق
- عرض التقييمات الأخيرة
- رسم بياني لتوزيع التقييمات

#### `ToolRatingDisplay.tsx` - مكون العرض المصغر:
- عرض النجوم والمتوسط في بطاقات الأدوات
- دعم أحجام مختلفة (sm, md)
- حالة تحميل متحركة

## التكامل مع الصفحات

### 1. صفحة الأداة الفردية (`/tools/[slug]`)
```tsx
<ToolRatingComponent 
    toolSlug={slug} 
    toolName={tool.name} 
/>
```

### 2. الصفحة الرئيسية وصفحات الفئات
```tsx
<ToolRatingDisplay 
    toolSlug={tool.slug} 
    showCount={true} 
    size="sm" 
/>
```

## الميزات

### ✅ المنجز:
- [x] إنشاء قاعدة البيانات والجداول
- [x] تطوير مكتبة API كاملة
- [x] مكون التقييم الرئيسي
- [x] مكون العرض المصغر
- [x] التكامل مع صفحات الأدوات
- [x] إدارة الجلسات بدون تسجيل دخول
- [x] منع التقييم المتكرر
- [x] عرض الإحصائيات والرسوم البيانية
- [x] إضافة التعليقات
- [x] عرض التقييمات الأخيرة
- [x] تحديث الإحصائيات تلقائياً

### 🔄 قيد التطوير:
- [ ] إضافة المزيد من البيانات التجريبية
- [ ] تحسين تصميم الواجهة
- [ ] إضافة فلترة وترتيب التقييمات
- [ ] تحليلات متقدمة للتقييمات

## الاستخدام

### إضافة تقييم:
1. المستخدم يختار عدد النجوم (1-5)
2. يمكن إضافة تعليق اختياري
3. يتم حفظ التقييم مع معرف الجلسة
4. تحديث الإحصائيات تلقائياً

### عرض التقييمات:
- متوسط التقييم مع النجوم
- عدد التقييمات الإجمالي
- توزيع التقييمات (1-5 نجوم)
- التقييمات الأخيرة مع التعليقات

## الأمان والخصوصية

- لا يتم حفظ معلومات شخصية
- استخدام معرف جلسة عشوائي
- حفظ عنوان IP للحماية من الإساءة
- التحقق من صحة البيانات
- منع SQL Injection عبر Supabase

## التطوير المستقبلي

1. **تحسينات الواجهة:**
   - رسوم بيانية أكثر تفاعلية
   - تصميم محسن للموبايل
   - إضافة رموز تعبيرية للتقييمات

2. **ميزات متقدمة:**
   - تصنيف التقييمات (مفيد/غير مفيد)
   - فلترة حسب التاريخ والتقييم
   - إحصائيات مفصلة للمطورين

3. **تحليلات:**
   - تقارير شهرية للتقييمات
   - مقارنة أداء الأدوات
   - اتجاهات التقييمات عبر الزمن


'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  text: z.string().min(1, { message: 'الرجاء إدخال النص' }),
  count: requiredNumber().int().min(1).max(10000, { message: 'الحد الأقصى 10000 مرة' }).default(10),
  separator: z.string().default('newline'),
  customSeparator: z.string().optional(),
  addNumbering: z.boolean().default(false),
});

export function TextRepeaterTool() {
  const [result, setResult] = useState<string>('');
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
      count: 10,
      separator: 'newline',
      customSeparator: '',
      addNumbering: false,
    },
  });

  const watchSeparator = form.watch('separator');

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { text, count, separator, customSeparator, addNumbering } = data;
    
    let actualSeparator = '';
    switch (separator) {
      case 'newline':
        actualSeparator = '\n';
        break;
      case 'space':
        actualSeparator = ' ';
        break;
      case 'comma':
        actualSeparator = '، ';
        break;
      case 'custom':
        actualSeparator = customSeparator || '';
        break;
      default:
        actualSeparator = '\n';
    }
    
    let repeatedText = '';
    for (let i = 1; i <= count; i++) {
      const prefix = addNumbering ? `${i}. ` : '';
      repeatedText += prefix + text;
      if (i < count) {
        repeatedText += actualSeparator;
      }
    }
    
    setResult(repeatedText);
  }

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result).then(() => {
        toast({
          title: "تم النسخ",
          description: "تم نسخ النص إلى الحافظة",
        });
      });
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تكرار النص</CardTitle>
        <CardDescription>تكرار نص أو كلمة لعدد معين من المرات</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="text"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>النص المراد تكراره</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="أدخل النص هنا" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="count"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>عدد مرات التكرار</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} min={1} max={10000} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="separator"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الفاصل بين التكرارات</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الفاصل" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="newline">سطر جديد</SelectItem>
                      <SelectItem value="space">مسافة</SelectItem>
                      <SelectItem value="comma">فاصلة</SelectItem>
                      <SelectItem value="custom">فاصل مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            
            {watchSeparator === 'custom' && (
              <FormField
                control={form.control}
                name="customSeparator"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الفاصل المخصص</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="أدخل الفاصل المخصص" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            <FormField
              control={form.control}
              name="addNumbering"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">إضافة ترقيم</FormLabel>
                    <FormDescription>
                      إضافة أرقام تسلسلية قبل كل تكرار
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <Button type="submit" className="w-full">
              تكرار النص
            </Button>
          </form>
        </Form>
        
        {result && (
          <div className="mt-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">النتيجة:</h3>
              <Button variant="outline" size="sm" onClick={copyToClipboard}>
                <Copy className="h-4 w-4 ml-2" />
                نسخ
              </Button>
            </div>
            <div className="p-4 bg-secondary/20 rounded-md h-[200px] overflow-auto">
              <pre className="whitespace-pre-wrap">{result}</pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

# أدوات بالعربي - مجموعة أدوات عربية شاملة

موقع إلكتروني مجاني يوفر مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية.

## المميزات

- 🌐 **واجهة عربية كاملة** - تصميم متجاوب يدعم اللغة العربية
- 🔧 **أدوات متنوعة** - محولات التاريخ، حاسبات مالية، أدوات نصوص، أدوات ذكاء اصطناعي
- 📱 **متجاوب** - يعمل على جميع الأجهزة
- 🚀 **سريع وآمن** - معظم الأدوات تعالج البيانات محلياً في متصفحك
- 💰 **مجاني بالكامل** - جميع الأدوات متاحة للاستخدام مجاناً

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم
- **Radix UI** - مكونات واجهة المستخدم
- **Supabase** - قاعدة البيانات والاستضافة
- **Genkit** - للذكاء الاصطناعي

## الإعداد للتطوير

1.  **استنساخ المشروع**
    ```bash
    git clone https://github.com/Ridanotx/Jami3Eladawat.git
    cd Jami3Eladawat
    ```

2.  **تثبيت التبعيات**
    ```bash
    npm install
    ```

3.  **إعداد متغيرات البيئة (مهم جداً)**
    أنشئ ملفاً جديداً باسم `.env.local` في جذر المشروع وأضف المتغيرات التالية:
    ```env
    # مفتاح Gemini API مطلوب لأدوات الذكاء الاصطناعي
    GEMINI_API_KEY='YOUR_GEMINI_API_KEY_HERE'
    
    # لتجاوز الحد الأقصى للاستخدام اليومي للأدوات (اختياري)
    # اضبطه على 'true' لتمكين وضع المطور
    NEXT_PUBLIC_ADMIN_BYPASS_LIMIT=true
    
    # إعدادات الموقع الأساسية (اختياري)
    NEXT_PUBLIC_SITE_URL=https://adawat.org/
    NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
    NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXXXXXXXXX
    ```
    **ملاحظة هامة:** الأدوات التي تعتمد على الذكاء الاصطناعي (مثل إزالة خلفية الصور، تلخيص النصوص، إعادة الصياغة) **لن تعمل بدون `GEMINI_API_KEY`**. أنت مسؤول عن تكاليف استخدام هذا المفتاح، ولكن Google توفر باقة مجانية سخية كافية للاستخدام والتطوير.

4.  **تشغيل الخادم المحلي**
    ```bash
    npm run dev
    ```

افتح [http://localhost:9003](http://localhost:9003) في متصفحك.

## المساهمة

نرحب بالمساهمات! يرجى:

1.  عمل Fork للمشروع
2.  إنشاء فرع جديد للميزة
3.  إجراء التغييرات
4.  إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## التواصل

-   الموقع: [adawat.org](https://adawat.org/)
-   البريد الإلكتروني: <EMAIL>

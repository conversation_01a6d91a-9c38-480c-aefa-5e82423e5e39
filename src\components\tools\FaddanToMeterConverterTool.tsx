
'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const FADDAN_TO_METER_FACTOR = 4200.83;

export function FaddanToMeterConverterTool() {
  const [faddan, setFaddan] = useState('1');
  const [meters, setMeters] = useState(FADDAN_TO_METER_FACTOR.toString());

  const handleFaddanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFaddan(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setMeters((numValue * FADDAN_TO_METER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 2, useGrouping: false}));
    } else {
      setMeters('');
    }
  };

  const handleMetersChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMeters(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setFaddan((numValue / FADDAN_TO_METER_FACTOR).toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setFaddan('');
    }
  };

  const handleSwap = () => {
    const currentFaddan = faddan;
    const currentMeters = meters;
    setFaddan(currentMeters);
    setMeters(currentFaddan);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من فدان إلى متر مربع (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري لمساحة الأرض.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 فدان = 4200.83 متر مربع
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="faddan" className="text-sm font-medium mb-2 block">
              فدان
            </label>
            <Input
              id="faddan"
              type="number"
              value={faddan}
              onChange={handleFaddanChange}
              placeholder="أدخل المساحة بالفدان"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="meters" className="text-sm font-medium mb-2 block">
              متر مربع (م²)
            </label>
            <Input
              id="meters"
              type="number"
              value={meters}
              onChange={handleMetersChange}
              placeholder="أدخل المساحة بالمتر المربع"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

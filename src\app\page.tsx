
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { toolCategories } from '@/lib/tools';
import ToolRatingDisplay from '@/components/ToolRatingDisplay';
import type { Metadata } from 'next';

const faqSchema = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "ما هي أدوات بالعربي؟",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "أدوات بالعربي هي منصة مجانية توفر أكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي، تشمل محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، حاسبة العمر، وغيرها من الأدوات المفيدة."
      }
    },
    {
      "@type": "Question",
      "name": "هل أدوات بالعربي مجانية؟",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "نعم، جميع أدواتنا مجانية 100% ولا تتطلب أي تسجيل أو اشتراك."
      }
    },
    {
      "@type": "Question",
      "name": "ما هي أشهر الأدوات المتوفرة؟",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "من أشهر أدواتنا: محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، حاسبة العمر، حاسبة BMI، محول QR كود، وعدد كبير من الحاسبات المالية والصحية."
      }
    },
    {
      "@type": "Question",
      "name": "هل الأدوات دقيقة وموثوقة؟",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "نعم، جميع أدواتنا تعتمد على خوارزميات دقيقة ويتم تحديثها باستمرار لضمان أعلى درجات الدقة والموثوقية."
      }
    }
  ]
};

export const metadata: Metadata = {
  title: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} | محول التاريخ وحاسبة الزكاة`,
  description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، حاسبة العمر، وأكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي. جرب الآن!`,
  keywords: [
    'أدوات عربية مجانية', 'محول التاريخ الهجري', 'حاسبة الزكاة الإلكترونية', 'حاسبة العمر بالهجري',
    'محول العملات العربية', 'عداد الكلمات العربي', 'أدوات النصوص المتقدمة', 'حاسبة مالية ذكية',
    'أدوات رقمية عربية', 'موقع أدوات مجاني', 'حاسبات إسلامية دقيقة', 'محولات عربية احترافية',
    'أدوات أونلاين بالعربي', 'تطبيقات حسابية عربية', 'أدوات productivity عربية', 'حاسبة BMI العربية',
    'محول QR كود عربي', 'أدوات تحويل الوحدات', 'حاسبة النسبة المئوية', 'أدوات التخطيط المالي',
    'حاسبة الضريبة العربية', 'محول الوزن والطول', 'أدوات التاريخ الهجري', 'حاسبة الحمل والولادة',
    'أدوات النص العربي المتقدمة', 'حاسبة الاستثمار', 'أدوات العملات الرقمية', 'محول الوقت العالمي',
    `أدوات ${new Date().getFullYear()}`, `حاسبات ${new Date().getFullYear()}`, 'أفضل أدوات عربية',
    'Arabic tools free', 'Hijri date converter', 'Zakat calculator arabic', 'Age calculator islamic'
  ],
  openGraph: {
    title: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()}`,
    description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، وأكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي.`,
    type: 'website',
    locale: 'ar_SA',
    siteName: 'أدوات بالعربي',
    images: [
      {
        url: '/images/og-homepage.jpg',
        width: 1200,
        height: 630,
        alt: 'أدوات بالعربي - مجموعة أدوات عربية مجانية',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: `أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()}`,
    description: `أفضل مجموعة أدوات عربية مجانية ${new Date().getFullYear()} ✅ محول التاريخ الهجري، حاسبة الزكاة، عداد الكلمات، محول العملات، وأكثر من 80 أداة ذكية.`,
    images: ['/images/og-homepage.jpg'],
    creator: '@adawat_org',
  },
  alternates: {
    canonical: '/',
  },
  verification: {
    google: 'google-site-verification-code',
  },
  other: {
    'msvalidate.01': 'bing-verification-code',
    'yandex-verification': 'yandex-verification-code',
  }
};

export default function Home() {
  return (
    <div className="flex-1 w-full">
      <section className="w-full py-16 md:py-24">
        <div className="container-full">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-headline font-bold tracking-tighter sm:text-5xl">
              أدوات بالعربي - أفضل مجموعة أدوات عربية مجانية شاملة
            </h1>
            <p className="mt-4 max-w-4xl mx-auto text-muted-foreground md:text-xl">
              اكتشف أفضل مجموعة أدوات عربية مجانية شاملة لعام {new Date().getFullYear()}، تضم أكثر من 80 أداة ذكية مصممة خصيصاً للمستخدم العربي. استخدم محول التاريخ الهجري والميلادي بدقة، احسب زكاتك بسهولة، حوّل العملات بأسعار الصرف المباشرة، عد كلماتك، واستمتع بأدوات متقدمة للمحاسبة، الصحة، التعليم، والحياة اليومية. جميع أدواتنا مجانية 100% ولا تتطلب تسجيلاً.
            </p>
            <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/articles">
                <Button size="lg" className="w-full sm:w-auto">
                  تصفح المقالات التعليمية
                  <ArrowLeft className="mr-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="/p/guide">
                <Button  size="lg" className="w-full sm:w-auto">
                  دليل الاستخدام
                </Button>
              </Link>
            </div>
            <div className="mt-6 flex flex-wrap justify-center gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">محول التاريخ</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">حاسبة الزكاة</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">عداد الكلمات</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">محول العملات</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">أدوات مجانية</span>
            </div>
          </div>

          
          {toolCategories.map((category) => {
            const availableTools = category.tools;
            if (availableTools.length === 0) return null;

            return (
              <div key={category.slug} className="mb-16">
                <div className="flex items-center gap-4 mb-8">
                  <category.icon className="w-8 h-8 text-primary" />
                  <h2 className="text-3xl font-headline font-bold tracking-tighter">
                    {category.name}
                  </h2>
                </div>
                 <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {availableTools.map((tool) => (
                    <Link
                      key={tool.name}
                      href={tool.path}
                      className="group">
                      <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                  {tool.icon && (
                                      <div className="p-2 rounded-full bg-primary/10 text-primary">
                                          <tool.icon className="w-5 h-5" />
                                      </div>
                                  )}
                                  <CardTitle className="font-headline text-lg">{tool.name}</CardTitle>
                              </div>
                              <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-3">
                            {tool.description}
                          </p>
                          <ToolRatingDisplay toolSlug={tool.slug} showCount={true} size="sm" />
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            );
          })}

          {/* Featured Articles Section */}
          <div className="mt-20 mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-headline font-bold tracking-tighter mb-4">
                مقالات مفيدة ونصائح عملية
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                تعلم كيفية استخدام الأدوات بفعالية أكبر من خلال مقالاتنا التعليمية المفصلة
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <Link href="/articles/how-to-calculate-zakat" className="group">
                <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                  <CardHeader>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      كيفية حساب الزكاة بطريقة صحيحة
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      دليل شامل لحساب زكاة المال والذهب والفضة وفقاً للأحكام الشرعية
                    </p>
                    <div className="mt-4 flex items-center gap-2 text-xs text-muted-foreground">
                      <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">إسلامية</span>
                      <span>5 دقائق قراءة</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/articles/qr-code-complete-guide" className="group">
                <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                  <CardHeader>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      الدليل الشامل لرموز QR
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      تعلم كل شيء عن رموز QR وكيفية إنشائها واستخدامها في الأعمال
                    </p>
                    <div className="mt-4 flex items-center gap-2 text-xs text-muted-foreground">
                      <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">تقنية</span>
                      <span>8 دقائق قراءة</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/articles/bmi-health-guide" className="group">
                <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                  <CardHeader>
                    <CardTitle className="text-lg group-hover:text-primary transition-colors">
                      فهم مؤشر كتلة الجسم وأهميته
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      دليل شامل لفهم مؤشر كتلة الجسم وكيفية حسابه وتفسير النتائج
                    </p>
                    <div className="mt-4 flex items-center gap-2 text-xs text-muted-foreground">
                      <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">صحة</span>
                      <span>6 دقائق قراءة</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            <div className="text-center">
              <Link href="/articles">
                <Button size="lg">
                  عرض جميع المقالات
                  <ArrowLeft className="mr-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section for SEO */}
      <section className="w-full py-16 bg-muted/50">
        <div className="container-full">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-headline font-bold tracking-tighter mb-4">
              الأسئلة الشائعة حول أدوات بالعربي
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              إجابات على أكثر الأسئلة التي يطرحها المستخدمون حول أدواتنا المجانية
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">ما هي أدوات بالعربي؟</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  أدوات بالعربي هي منصة مجانية متخصصة توفر أكثر من 80 أداة ذكية مصممة خصيصاً لتلبية احتياجات المستخدم العربي. تشمل محول التاريخ الهجري والميلادي بدقة، حاسبة الزكاة الشرعية، عداد الكلمات، محول العملات بأسعار الصرف المباشرة، حاسبة العمر، وغيرها من الأدوات المفيدة في الحياة اليومية.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">هل أدوات بالعربي مجانية بالكامل؟</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  نعم، جميع أدواتنا مجانية 100% ولا تتطلب أي تسجيل أو اشتراك. يمكنك استخدام أي أداة بشكل غير محدود دون الحاجة لإنشاء حساب أو تقديم أي معلومات شخصية.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">ما هي أشهر الأدوات المتوفرة؟</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  من أشهر أدواتنا: محول التاريخ الهجري والميلادي بدقة عالية، حاسبة الزكاة وفقاً للأحكام الشرعية، عداد الكلمات للنصوص العربية، محول العملات بأسعار الصرف المباشرة، حاسبة العمر بالهجري والميلادي، حاسبة BMI للصحة، محول QR كود، وحاسبات مالية متخصصة.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">هل الأدوات دقيقة وموثوقة؟</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  نعم، جميع أدواتنا تعتمد على خوارزميات دقيقة ويتم تحديثها باستمرار. محول التاريخ يستخدم أحدث الحسابات الفلكية، حاسبة الزكاة تتبع الأحكام الشرعية المعتمدة، ومحول العملات يستخدم أسعار الصرف المباشرة من مصادر موثوقة.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Add FAQ Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema)
        }}
      />
    </div>
  );
}

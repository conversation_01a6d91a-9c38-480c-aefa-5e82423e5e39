
'use client';

import { useState, useTransition, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Sparkles, Globe, Loader2, ArrowDown, ArrowUp } from 'lucide-react';
import { COUNTRIES_CURRENCIES } from '@/lib/constants/currencies';
import { getGoldPrice } from '@/lib/actions/gold';
import { cn } from '@/lib/utils';

type PriceData = Awaited<ReturnType<typeof getGoldPrice>>;

interface GoldPriceToolProps {
  initialData: PriceData;
}

export function GoldPriceTool({ initialData }: GoldPriceToolProps) {
  const [currentData, setCurrentData] = useState<PriceData>(initialData);
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    const timer = setInterval(() => {
      startTransition(async () => {
        try {
            const newData = await getGoldPrice(currentData.selectedCurrency);
            if (newData.success) {
                setCurrentData(newData);
            }
        } catch (error) {
            console.error("Failed to refresh gold price:", error);
        }
      });
    }, 60000); // 60 seconds

    return () => clearInterval(timer);
  }, [currentData.selectedCurrency]);


  const { success, prices, timestamp, error, source, selectedCurrency, countryInfo, karats, change, changePercent } = currentData;

  const handleCurrencyChange = (newCurrency: string) => {
    startTransition(async () => {
      const newData = await getGoldPrice(newCurrency);
      setCurrentData(newData);
    });
  };

  if (!success && !currentData.prices) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
        <AlertDescription>
          لم نتمكن من تحميل أسعار الذهب. الرجاء المحاولة مرة أخرى لاحقًا.
          <p className="mt-2 text-xs">{error}</p>
        </AlertDescription>
      </Alert>
    )
  }

  const sortedKarats = karats 
    ? Object.entries(karats).sort(([a], [b]) => parseInt(b) - parseInt(a)) 
    : [];

  const unitsAndKarats = [
    ...sortedKarats.map(([karat, priceLocal]) => ({
        unit: `عيار ${karat}`,
        price_local: priceLocal,
        price_usd: priceLocal && prices.local.perGram && prices.usd.perGram ? priceLocal / (prices.local.perGram / prices.usd.perGram) : 0,
    })),
    { unit: 'الكيلو (kg)', price_local: prices.local.perKilo, price_usd: prices.usd.perKilo },
    { unit: 'الجرام (g)', price_local: prices.local.perGram, price_usd: prices.usd.perGram },
    { unit: 'الأونصة (oz)', price_local: prices.local.perOunce, price_usd: prices.usd.perOunce },
  ];
  
  const isPositive = (change || 0) >= 0;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
            <div className='flex items-center gap-2'>
              {isPending && <Loader2 className="h-4 w-4 animate-spin text-blue-500" />}
              أسعار الذهب اليوم
              <Sparkles className="text-yellow-500" />
            </div>
            <div className={`flex items-center gap-2 font-mono text-base ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
                <span>{isPositive ? '+' : ''}{(change || 0).toFixed(2)}</span>
                <span>({isPositive ? '+' : ''}{(changePercent || 0).toFixed(2)}%)</span>
                {isPositive ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
            </div>
        </CardTitle>
        <CardDescription>
          آخر تحديث: {new Date((timestamp || 0) * 1000).toLocaleString('ar-SA-u-nu-latn', { dateStyle: 'medium', timeStyle: 'short' })}
          {source && <span className="mx-2">•</span>}
          {source && <span>المصدر: {source}</span>}
          {error && (
            <span className="block text-amber-600 text-xs mt-1">تنبيه: {error}</span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <div className="flex items-center gap-4">
            <Globe className="h-5 w-5 text-muted-foreground" />
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">اختر الدولة والعملة:</label>
              <Select 
                value={selectedCurrency} 
                onValueChange={handleCurrencyChange}
                disabled={isPending}
              >
                <SelectTrigger className="w-full md:w-80">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(COUNTRIES_CURRENCIES).map(([code, info]) => (
                    <SelectItem key={code} value={code}>
                      <div className="flex flex-col">
                        <span className="font-medium">{info.name}</span>
                        <span className="text-xs text-muted-foreground">{info.currency} ({code})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {countryInfo && (
            <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>{countryInfo.name}</strong> - العملة: {countryInfo.currency}
              </p>
            </div>
          )}
        </div>
        
        <Table className="hidden md:table">
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">الوحدة / العيار</TableHead>
              <TableHead className="text-right">السعر ({countryInfo?.currency})</TableHead>
              <TableHead className="text-right">السعر (دولار أمريكي)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {unitsAndKarats.map((item) => (
              <TableRow key={item.unit}>
                <TableCell className="text-right">
                  <Badge variant="outline" className="text-lg border-yellow-500 text-yellow-600">{item.unit}</Badge>
                </TableCell>
                <TableCell className="text-right font-mono text-lg">
                  {(item.price_local || 0).toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} {selectedCurrency}
                </TableCell>
                <TableCell className="text-right font-mono text-lg">
                  ${(item.price_usd || 0).toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <div className="md:hidden space-y-3">
           {unitsAndKarats.map((item) => (
            <div key={item.unit} className="p-3 border rounded-lg">
                <div className="font-semibold mb-2">
                    <Badge variant="outline" className="text-base border-yellow-500 text-yellow-600">{item.unit}</Badge>
                </div>
                <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">السعر ({countryInfo?.currency}):</span>
                        <span className="font-mono font-medium">{(item.price_local || 0).toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">السعر (USD):</span>
                        <span className="font-mono font-medium">${(item.price_usd || 0).toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                    </div>
                </div>
            </div>
            ))}
        </div>
        
        <p className="text-xs text-muted-foreground mt-4">
          الأسعار المقدمة محدثة من مصادر موثوقة وهي لأغراض إعلامية فقط. قد تختلف الأسعار الفعلية حسب السوق المحلي والتجار.
        </p>
      </CardContent>
    </Card>
  );
}

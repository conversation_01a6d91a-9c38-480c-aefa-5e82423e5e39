
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Calculator, Delete } from 'lucide-react';

export function SimpleCalculatorTool() {
  const [displayValue, setDisplayValue] = useState('0');
  const [firstOperand, setFirstOperand] = useState<number | null>(null);
  const [operator, setOperator] = useState<string | null>(null);
  const [waitingForSecondOperand, setWaitingForSecondOperand] = useState(false);
  const [history, setHistory] = useState('');

  const handleNumberClick = (numStr: string) => {
    if (waitingForSecondOperand) {
      setDisplayValue(numStr);
      setWaitingForSecondOperand(false);
    } else {
      setDisplayValue(displayValue === '0' ? numStr : displayValue + numStr);
    }
  };

  const handleDecimalClick = () => {
    if (waitingForSecondOperand) {
      setDisplayValue('0.');
      setWaitingForSecondOperand(false);
      return;
    }
    if (!displayValue.includes('.')) {
      setDisplayValue(displayValue + '.');
    }
  };
  
  const performCalculation = (op1: number, op2: number, currentOperator: string | null): number => {
    if (currentOperator === '+') return op1 + op2;
    if (currentOperator === '-') return op1 - op2;
    if (currentOperator === '×') return op1 * op2;
    if (currentOperator === '÷') {
        if (op2 === 0) return NaN; // Division by zero
        return op1 / op2;
    }
    return op2;
  };

  const handleOperatorClick = (nextOperator: string) => {
    const inputValue = parseFloat(displayValue);

    if (operator && !waitingForSecondOperand) {
      const result = performCalculation(firstOperand!, inputValue, operator);
      const formattedResult = parseFloat(result.toPrecision(15)).toString();
      setDisplayValue(formattedResult);
      setFirstOperand(result);
      setHistory(`${formattedResult} ${nextOperator}`);
    } else {
      setFirstOperand(inputValue);
      setHistory(`${inputValue} ${nextOperator}`);
    }
    
    setWaitingForSecondOperand(true);
    setOperator(nextOperator);
  };

  const handleEqualsClick = () => {
    if (!operator || firstOperand === null) return;
    
    const inputValue = parseFloat(displayValue);
    const result = performCalculation(firstOperand, inputValue, operator);
    
    if (isNaN(result)) {
      setDisplayValue('خطأ');
      setHistory('');
    } else {
      const formattedResult = parseFloat(result.toPrecision(15)).toString();
      setHistory(`${firstOperand} ${operator} ${inputValue} =`);
      setDisplayValue(formattedResult);
    }

    setFirstOperand(null);
    setOperator(null);
    setWaitingForSecondOperand(true);
  };
  
  const handleClearClick = () => {
    setDisplayValue('0');
    setFirstOperand(null);
    setOperator(null);
    setWaitingForSecondOperand(false);
    setHistory('');
  };

  const handleBackspace = () => {
    if (displayValue !== '0' && displayValue !== 'خطأ') {
        if (displayValue.length > 1) {
            setDisplayValue(displayValue.slice(0, -1));
        } else {
            setDisplayValue('0');
        }
    }
  };

  const handlePlusMinusClick = () => {
    if (displayValue !== '0' && displayValue !== 'خطأ') {
      setDisplayValue(String(parseFloat(displayValue) * -1));
    }
  };

  const handlePercentClick = () => {
    if (displayValue !== 'خطأ') {
      setDisplayValue(String(parseFloat(displayValue) / 100));
    }
  };
  
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;
      if (key >= '0' && key <= '9') handleNumberClick(key);
      else if (key === '.') handleDecimalClick();
      else if (key === '+') handleOperatorClick('+');
      else if (key === '-') handleOperatorClick('-');
      else if (key === '*') handleOperatorClick('×');
      else if (key === '/') handleOperatorClick('÷');
      else if (key === '%') handlePercentClick();
      else if (key === 'Enter' || key === '=') handleEqualsClick();
      else if (key === 'Escape' || key.toLowerCase() === 'c') handleClearClick();
      else if (key === 'Backspace') handleBackspace();
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [displayValue, firstOperand, operator, waitingForSecondOperand]);


  const calculatorButtons = [
    { value: 'C', onClick: handleClearClick, className: 'bg-muted hover:bg-muted/80' },
    { value: '±', onClick: handlePlusMinusClick, className: 'bg-muted hover:bg-muted/80' },
    { value: '%', onClick: handlePercentClick, className: 'bg-muted hover:bg-muted/80' },
    { value: '÷', onClick: () => handleOperatorClick('÷'), className: 'bg-primary/80 hover:bg-primary text-white' },

    { value: '7', onClick: () => handleNumberClick('7') },
    { value: '8', onClick: () => handleNumberClick('8') },
    { value: '9', onClick: () => handleNumberClick('9') },
    { value: '×', onClick: () => handleOperatorClick('×'), className: 'bg-primary/80 hover:bg-primary text-white' },

    { value: '4', onClick: () => handleNumberClick('4') },
    { value: '5', onClick: () => handleNumberClick('5') },
    { value: '6', onClick: () => handleNumberClick('6') },
    { value: '-', onClick: () => handleOperatorClick('-'), className: 'bg-primary/80 hover:bg-primary text-white' },

    { value: '1', onClick: () => handleNumberClick('1') },
    { value: '2', onClick: () => handleNumberClick('2') },
    { value: '3', onClick: () => handleNumberClick('3') },
    { value: '+', onClick: () => handleOperatorClick('+'), className: 'bg-primary/80 hover:bg-primary text-white' },

    { value: '0', onClick: () => handleNumberClick('0'), className: 'col-span-2' },
    { value: '.', onClick: handleDecimalClick },
    { value: '=', onClick: handleEqualsClick, className: 'bg-primary hover:bg-primary/90 text-white' },
  ];

  return (
    <Card className="w-full max-w-sm mx-auto shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
            <Calculator className="h-6 w-6" />
            آلة حاسبة
        </CardTitle>
        <CardDescription className="text-center">لإجراء العمليات الحسابية الأساسية.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-muted p-4 rounded-lg mb-4 text-left">
            <div className="font-mono text-sm text-muted-foreground break-all h-6" dir="ltr">{history}</div>
            <div className="font-mono text-4xl text-foreground break-all h-12" dir="ltr">{displayValue}</div>
        </div>
        <div className="grid grid-cols-4 gap-2">
            {calculatorButtons.map((btn, index) => (
                <Button 
                    key={index}
                    onClick={btn.onClick}
                    className={`h-16 text-xl font-semibold ${btn.className || ''}`}
                    variant="outline"
                >
                    {btn.value}
                </Button>
            ))}
        </div>
      </CardContent>
    </Card>
  );
}

'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Download, Type, Loader2 } from 'lucide-react';
import { jsPDF } from 'jspdf';

export function TextToPdfTool() {
  const [text, setText] = useState('');
  const [isConverting, setIsConverting] = useState(false);
  const { toast } = useToast();

  const handleConvertToPdf = async () => {
    if (!text.trim()) {
      toast({ title: "الرجاء إدخال نص أولاً.", variant: "destructive" });
      return;
    }

    setIsConverting(true);
    toast({ title: "جاري إنشاء ملف PDF..." });

    try {
      const doc = new jsPDF({
        orientation: 'p',
        unit: 'mm',
        format: 'a4',
      });

      // Basic support for Arabic characters (may not connect correctly without a proper font)
      doc.setR2L(true);
      doc.setFont('Helvetica'); // Fallback to a standard font
      
      const fontSize = 12;
      const margin = 20;
      const pageWidth = doc.internal.pageSize.getWidth();
      const textWidth = pageWidth - margin * 2;

      doc.setFontSize(fontSize);
      
      // Split text into lines that fit the page width
      const lines = doc.splitTextToSize(text, textWidth);

      let y = margin;
      const pageHeight = doc.internal.pageSize.getHeight();
      const lineHeight = fontSize * 0.5;

      for (let i = 0; i < lines.length; i++) {
        // Add a new page if the current line exceeds the page height
        if (y + lineHeight > pageHeight - margin) {
          doc.addPage();
          y = margin; // Reset y position for the new page
        }
        // For basic RTL text, we align it to the right.
        doc.text(lines[i], pageWidth - margin, y, { align: 'right' });
        y += lineHeight; 
      }
      
      doc.save('document.pdf');
      
      toast({ title: "تم إنشاء ملف PDF بنجاح!", description: "بدأ تحميل الملف." });
    } catch (error) {
      console.error("PDF generation error:", error);
      toast({ title: "خطأ", description: "حدث خطأ أثناء إنشاء ملف PDF.", variant: "destructive" });
    } finally {
      setIsConverting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل نص إلى PDF</CardTitle>
        <CardDescription>اكتب أو الصق نصًا هنا لتحويله مباشرةً إلى مستند PDF.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="relative">
          <Type className="absolute top-3 right-3 h-5 w-5 text-muted-foreground" />
          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="اكتب أو الصق النص هنا..."
            className="min-h-[300px] text-lg leading-loose p-4 pr-10"
            dir="rtl"
          />
        </div>
        <Button onClick={handleConvertToPdf} disabled={isConverting} className="w-full">
          {isConverting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Download className="ml-2 h-4 w-4" />}
          {isConverting ? "جاري التحويل..." : "تحويل وتحميل PDF"}
        </Button>
      </CardContent>
    </Card>
  );
}

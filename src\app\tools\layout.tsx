import { toolCategories } from "@/lib/tools";
import { loadToolContent } from '@/lib/content/loader';
import type { Metadata, ResolvingMetadata } from 'next';
import { notFound } from 'next/navigation';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

// This function now runs in the layout (a Server Component)
export async function generateMetadata(
  { params }: { params: { slug: string } },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = params;
  const tool = toolCategories.flatMap(c => c.tools).find(t => t.slug === slug);

  if (!tool) {
    return {
      title: 'أداة غير موجودة',
      description: 'الأداة المطلوبة غير موجودة',
    };
  }

  const content = await loadToolContent(slug);
  const currentYear = new Date().getFullYear();
  const description = content.seoDescription?.replace(/<[^>]*>?/gm, '').substring(0, 160) || tool.description;
  const enhancedDescription = `${description} - أداة مجانية ${currentYear}`;
  const previousImages = (await parent).openGraph?.images || [];
  const toolUrl = siteUrl ? `${siteUrl}/tools/${slug}` : `/tools/${slug}`;
  const category = toolCategories.find(cat => cat.tools.some(t => t.slug === slug));

  const keywords = [
    tool.name,
    category?.name || '',
    'أدوات عربية',
    'حاسبة مجانية',
    'أدوات مجانية',
    `أدوات ${currentYear}`,
    'Arabic tools',
    'free calculator',
    tool.name.replace(/حاسبة|محول|أداة/g, '').trim()
  ].filter(Boolean);

  return {
    title: `${tool.name} - أداة مجانية ${currentYear}`,
    description: enhancedDescription,
    keywords: keywords.join(', '),
    alternates: {
      canonical: `/tools/${slug}`,
    },
    openGraph: {
      title: `${tool.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      url: toolUrl,
      images: [...previousImages],
      type: 'article',
      locale: 'ar_SA',
      siteName: 'أدوات بالعربي',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${tool.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      images: [...previousImages],
      site: '@adawat_org',
      creator: '@adawat_org',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default function ToolsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="w-full flex-1 flex flex-col items-center p-4 sm:p-6 md:p-8">
      <div className="w-full max-w-4xl mx-auto">
        {children}
      </div>
    </div>
  );
}

// This is the correct place for generateStaticParams as layout is a Server Component
export async function generateStaticParams() {
  const paths = toolCategories
    .flatMap(category => category.tools)
    .filter(tool => !!tool.slug) // Ensure slug exists
    .map(tool => ({
      slug: tool.slug,
    }));

  return paths;
}


'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const TROY_OUNCE_IN_GRAMS = 31.1034768;

export function OunceToGramConverterTool() {
  const [ounces, setOunces] = useState('1');
  const [grams, setGrams] = useState(TROY_OUNCE_IN_GRAMS.toString());

  const handleOuncesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setOunces(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setGrams((numValue * TROY_OUNCE_IN_GRAMS).toLocaleString('en-US', {maximumFractionDigits: 5, useGrouping: false}));
    } else {
      setGrams('');
    }
  };

  const handleGramsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGrams(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setOunces((numValue / TROY_OUNCE_IN_GRAMS).toLocaleString('en-US', {maximumFractionDigits: 5, useGrouping: false}));
    } else {
      setOunces('');
    }
  };

  const handleSwap = () => {
    const currentOunces = ounces;
    const currentGrams = grams;
    setOunces(currentGrams);
    setGrams(currentOunces);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>اونصة الذهب كم جرام</CardTitle>
        <CardDescription>
          حوّل بسهولة بين أونصة تروي (وحدة قياس المعادن الثمينة) والجرام.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل المعتمد</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 أونصة تروي = 31.1034768 جرام
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="ounces" className="text-sm font-medium mb-2 block">
              أونصة تروي (Troy Ounce)
            </label>
            <Input
              id="ounces"
              type="number"
              value={ounces}
              onChange={handleOuncesChange}
              placeholder="أدخل الأونصات"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="grams" className="text-sm font-medium mb-2 block">
              جرام (Gram)
            </label>
            <Input
              id="grams"
              type="number"
              value={grams}
              onChange={handleGramsChange}
              placeholder="أدخل الجرامات"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
